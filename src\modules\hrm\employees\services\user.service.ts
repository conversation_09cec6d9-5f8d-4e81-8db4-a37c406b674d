import { Injectable, Logger } from '@nestjs/common';
// import { UserRepository } from '@/modules/auth/repositories/user.repository'; // DEPRECATED: User entity đã được xóa
import { EmployeeRepository } from '../repositories/employee.repository';
import { DepartmentRepository } from '../../org-units/repositories/department.repository';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserResponseDto } from '../dto/user-response.dto';
import { UpdateUserEmailDto } from '../dto/update-user-email.dto';
import { BulkDeleteUserDto } from '../dto/bulk-delete-user.dto';
import { BulkDeleteUserResponseDto } from '../dto/bulk-delete-user-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { Employee } from '../entities/employee.entity';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * Service xử lý logic nghiệp vụ cho người dùng
 * @deprecated Service này đã được chuyển đổi để sử dụng Employee entity thay vì User entity
 * Sẽ được thay thế hoàn toàn bằng EmployeeService trong tương lai
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    // private readonly userRepository: UserRepository, // REMOVED: User entity đã được xóa
    private readonly employeeRepository: EmployeeRepository,
    private readonly departmentRepository: DepartmentRepository,
  ) {
    this.logger.warn('⚠️  UserService is DEPRECATED. This service now uses Employee entity. Consider using EmployeeService instead.');
  }

  /**
   * Lấy danh sách người dùng với phân trang và lọc
   * @deprecated Sử dụng Employee entity thay vì User entity
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAllUsers(
    tenantId: number,
    query: UserQueryDto,
  ): Promise<PaginatedResult<UserResponseDto>> {
    try {
      this.logger.warn('⚠️  findAllUsers() is DEPRECATED. Using Employee entity instead of User entity.');
      
      // Chuyển đổi query để sử dụng với Employee entity
      const employeeQuery = {
        page: query.page || 1,
        limit: query.limit || 10,
        search: query.search,
        status: query.status as any, // Cast UserStatus to EmployeeStatus if needed
        sortBy: query.sortBy || 'id',
        sortOrder: (query as any).sortOrder || 'ASC', // Cast to access sortOrder
      };

      // Lấy employees có authentication data (email và password không null)
      const paginatedResult = await this.employeeRepository.findAll(tenantId, employeeQuery);

      // Map employees to UserResponseDto để maintain compatibility
      const items = await Promise.all(
        paginatedResult.items
          .filter(employee => employee.email && employee.password) // Chỉ lấy employees có authentication data
          .map((employee) => this.mapEmployeeToUserResponseDto(tenantId, employee))
      );

      return {
        items,
        meta: {
          ...paginatedResult.meta,
          totalItems: items.length, // Cập nhật lại total vì đã filter
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy danh sách người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết người dùng
   * @deprecated Sử dụng Employee entity thay vì User entity
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID người dùng
   * @returns Thông tin chi tiết người dùng
   */
  async findUserById(tenantId: number, id: number): Promise<UserResponseDto> {
    try {
      this.logger.warn('⚠️  findUserById() is DEPRECATED. Using Employee entity instead of User entity.');
      
      // Tìm employee bằng ID (vì User.id = Employee.id sau merge)
      const employee = await this.employeeRepository.findById(tenantId, id);
      if (!employee || !employee.email || !employee.password) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`,
        );
      }

      return await this.mapEmployeeToUserResponseDto(tenantId, employee);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết người dùng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy chi tiết người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi Employee entity sang UserResponseDto để maintain compatibility
   * @param tenantId ID tenant
   * @param employee Employee entity
   * @returns UserResponseDto
   */
  private async mapEmployeeToUserResponseDto(tenantId: number, employee: Employee): Promise<UserResponseDto> {
    // Employee entity đã chứa tất cả thông tin cần thiết
    this.logger.log(`🔗 [EMPLOYEE->USER] Mapping employee ${employee.id} to UserResponseDto`);

    let departmentName: string | null = null;

    // Lấy thông tin department nếu có
    if (employee.departmentId) {
      try {
        const department = await this.departmentRepository.findById(tenantId, employee.departmentId);
        if (department) {
          departmentName = department.name;
        }
      } catch (error) {
        this.logger.warn(`Could not fetch department info for departmentId: ${employee.departmentId}`);
      }
    }

    // Map Employee status to User status
    let userStatus: UserStatus = UserStatus.ACTIVE;
    if (employee.accountStatus === 'INACTIVE') {
      userStatus = UserStatus.INACTIVE;
    } else if (employee.accountStatus === 'PENDING') {
      userStatus = UserStatus.PENDING;
    }

    return {
      id: employee.id,
      email: employee.email || '',
      // Thông tin authentication từ Employee entity
      fullName: employee.employeeName || null,
      status: userStatus,
      createdAt: employee.createdAt || Date.now(),
      // Thông tin từ Employee entity
      employeeId: employee.id,
      employeeCode: employee.employeeCode || null,
      employeeName: employee.employeeName || null,
      departmentId: employee.departmentId || null,
      departmentName: departmentName,
      position: employee.jobTitle || null,
      dateOfBirth: employee.dateOfBirth ? employee.dateOfBirth.getTime() : null,
      gender: employee.gender || null,
      // Các field không có trong Employee entity
      phoneNumber: null, // Employee entity chưa có field này
      address: null, // Employee entity chưa có field này
      userType: null, // Có thể derive từ role/permission
    };
  }

  /**
   * Cập nhật email người dùng
   * @deprecated Sử dụng Employee entity thay vì User entity
   */
  async updateUserEmail(
    tenantId: number,
    userId: number,
    updateEmailDto: UpdateUserEmailDto,
  ): Promise<UserResponseDto> {
    this.logger.warn('⚠️  updateUserEmail() is DEPRECATED. Use EmployeeService instead.');
    throw new AppException(
      HRM_ERROR_CODES.USER_CREATION_FAILED,
      'updateUserEmail() is deprecated. Use EmployeeService instead.',
    );
  }

  /**
   * Xóa người dùng (soft delete)
   * @deprecated Sử dụng Employee entity thay vì User entity
   */
  async deleteUser(tenantId: number, userId: number): Promise<boolean> {
    this.logger.warn('⚠️  deleteUser() is DEPRECATED. Use EmployeeService instead.');
    throw new AppException(
      HRM_ERROR_CODES.USER_CREATION_FAILED,
      'deleteUser() is deprecated. Use EmployeeService instead.',
    );
  }

  /**
   * Xóa nhiều người dùng (bulk delete)
   * @deprecated Sử dụng Employee entity thay vì User entity
   */
  async bulkDeleteUsers(
    tenantId: number,
    dto: BulkDeleteUserDto,
  ): Promise<BulkDeleteUserResponseDto> {
    this.logger.warn('⚠️  bulkDeleteUsers() is DEPRECATED. Use EmployeeService instead.');
    throw new AppException(
      HRM_ERROR_CODES.USER_CREATION_FAILED,
      'bulkDeleteUsers() is deprecated. Use EmployeeService instead.',
    );
  }
}
