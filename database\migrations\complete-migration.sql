-- =====================================================
-- COMPLETE MIGRATION: Gộp Users vào Employees
-- Chạy tất cả trong 1 file SQL duy nhất
-- Ngày: 2025-06-18
-- =====================================================

BEGIN;

-- =====================================================
-- PHASE 1: BACKUP DỮ LIỆU
-- =====================================================

-- Backup bảng hiện tại
DROP TABLE IF EXISTS employees_backup_original;
CREATE TABLE employees_backup_original AS SELECT * FROM employees;

DROP TABLE IF EXISTS users_backup_original;
CREATE TABLE users_backup_original AS SELECT * FROM users;

-- =====================================================
-- PHASE 2: CẬP NHẬT CẤU TRÚC EMPLOYEES
-- =====================================================

-- Thêm các cột authentication và HR mới
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS account_status VARCHAR(20) DEFAULT 'PENDING',
ADD COLUMN IF NOT EXISTS tenant_id INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS employee_code VARCHAR(50),
ADD COLUMN IF NOT EXISTS employee_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS position VARCHAR(255),
ADD COLUMN IF NOT EXISTS department_id INTEGER,
ADD COLUMN IF NOT EXISTS manager_id INTEGER,
ADD COLUMN IF NOT EXISTS hire_date DATE,
ADD COLUMN IF NOT EXISTS employment_type VARCHAR(50) DEFAULT 'FULL_TIME',
ADD COLUMN IF NOT EXISTS salary DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
ADD COLUMN IF NOT EXISTS date_of_birth DATE,
ADD COLUMN IF NOT EXISTS gender VARCHAR(10),
ADD COLUMN IF NOT EXISTS marital_status VARCHAR(20),
ADD COLUMN IF NOT EXISTS emergency_contact_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS emergency_contact_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS bank_account_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS bank_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS social_insurance_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Cập nhật dữ liệu từ cột cũ
UPDATE employees 
SET 
    employee_name = COALESCE(full_name, email),
    status = CASE 
        WHEN enable = true THEN 'active'
        ELSE 'inactive'
    END,
    account_status = CASE 
        WHEN enable = true THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    employee_code = 'REDAI' || LPAD(id::text, 3, '0')
WHERE employee_name IS NULL OR status IS NULL OR account_status IS NULL;

-- Xóa các cột cũ
ALTER TABLE employees 
DROP COLUMN IF EXISTS full_name,
DROP COLUMN IF EXISTS enable,
DROP COLUMN IF EXISTS phone_number,
DROP COLUMN IF EXISTS address;

-- Đảm bảo constraints
ALTER TABLE employees
ALTER COLUMN email SET NOT NULL;

ALTER TABLE employees
ALTER COLUMN tenant_id SET NOT NULL;

ALTER TABLE employees
ALTER COLUMN employee_name SET NOT NULL;

ALTER TABLE employees
ALTER COLUMN employee_code SET NOT NULL;

-- =====================================================
-- PHASE 3: MIGRATE DỮ LIỆU TỪ USERS
-- =====================================================

-- Cập nhật employees có email trùng với users
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.is_active = true THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    date_of_birth = u.date_of_birth,
    gender = CASE 
        WHEN u.gender::text = 'male' THEN 'male'
        WHEN u.gender::text = 'female' THEN 'female'
        ELSE 'other'
    END,
    bank_account_number = u.account_number,
    tax_id = u.tax_code,
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Tạo employees mới cho users không có employee
INSERT INTO employees (
    email, password, account_status, employee_code, employee_name, status, tenant_id,
    date_of_birth, gender, bank_account_number, tax_id, employment_type, hire_date,
    created_at, updated_at
)
SELECT 
    u.email,
    u.password,
    CASE WHEN u.is_active = true THEN 'ACTIVE' ELSE 'INACTIVE' END,
    'REDAI' || LPAD((
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    )::text, 3, '0'),
    COALESCE(u.full_name, u.email),
    CASE WHEN u.is_active = true THEN 'active' ELSE 'inactive' END,
    1, -- Default tenant_id
    u.date_of_birth,
    CASE 
        WHEN u.gender::text = 'male' THEN 'male'
        WHEN u.gender::text = 'female' THEN 'female'
        ELSE 'other'
    END,
    u.account_number,
    u.tax_code,
    'FULL_TIME',
    CURRENT_DATE,
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
);

-- =====================================================
-- PHASE 4: CẬP NHẬT FOREIGN KEY REFERENCES
-- =====================================================

-- Tạo mapping table để track user_id -> employee_id
CREATE TEMP TABLE user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Cập nhật user_roles
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ur.user_id = m.user_id;

-- Cập nhật business_info
UPDATE business_info bi
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE bi.user_id = m.user_id;

-- Cập nhật two_factor_auth
UPDATE two_factor_auth tfa
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE tfa.user_id = m.user_id;

-- Cập nhật device_info
UPDATE device_info di
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE di.user_id = m.user_id;

-- Cập nhật auth_verification_logs
UPDATE auth_verification_logs avl
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE avl.user_id = m.user_id;

-- Cập nhật point_purchase_transactions
UPDATE point_purchase_transactions ppt
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ppt.user_id = m.user_id;

-- Cập nhật affiliate_accounts
UPDATE affiliate_accounts aa
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE aa.user_id = m.user_id;

-- Cập nhật affiliate_contracts
UPDATE affiliate_contracts ac
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ac.user_id = m.user_id;

-- =====================================================
-- PHASE 5: TẠO CONSTRAINTS VÀ INDEXES
-- =====================================================

-- Tạo constraints
ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_account_status_check 
CHECK (account_status IN ('ACTIVE', 'INACTIVE', 'PENDING'));

ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_employment_type_check 
CHECK (employment_type IN ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN'));

ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_status_check 
CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave'));

ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_gender_check 
CHECK (gender IN ('male', 'female', 'other'));

-- Tạo unique constraint
ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_employee_code_unique UNIQUE (employee_code);

-- Tạo indexes
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_email ON employees(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_employees_account_status ON employees(account_status);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX IF NOT EXISTS idx_employees_employee_code ON employees(employee_code);

-- =====================================================
-- PHASE 6: XÓA BẢNG USERS VÀ TẠO VIEW
-- =====================================================

-- Xóa foreign key constraints đến users
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'users'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
    END LOOP;
END $$;

-- Xóa bảng users
DROP TABLE IF EXISTS users CASCADE;

-- Tạo view users để tương thích ngược
CREATE OR REPLACE VIEW users AS
SELECT 
    id,
    employee_name as full_name,
    email,
    NULL as phone_number,
    CASE 
        WHEN account_status = 'ACTIVE' THEN true
        ELSE false
    END as is_active,
    false as is_verify_email,
    created_at,
    updated_at,
    NULL as citizen_issue_place,
    NULL as citizen_issue_date,
    false as is_first_password_change,
    NULL as country,
    NULL as address,
    tax_id as tax_code,
    0 as points_balance,
    'INDIVIDUAL' as type,
    NULL as platform,
    NULL as citizen_id,
    NULL as avatar,
    password,
    date_of_birth,
    gender,
    NULL as bank_code,
    bank_account_number as account_number,
    NULL as account_holder,
    NULL as bank_branch,
    'employee' as role,
    false as is_verify_phone,
    NULL as vector_store_key,
    NULL as alert_threshold,
    false as was_rpoint_alerted,
    tenant_id
FROM employees;

-- =====================================================
-- PHASE 7: TẠO FOREIGN KEY CONSTRAINTS MỚI
-- =====================================================

-- Tạo foreign key constraints mới reference đến employees
ALTER TABLE user_roles 
ADD CONSTRAINT user_roles_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE business_info 
ADD CONSTRAINT business_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE two_factor_auth 
ADD CONSTRAINT two_factor_auth_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE device_info 
ADD CONSTRAINT device_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE auth_verification_logs 
ADD CONSTRAINT auth_verification_logs_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE point_purchase_transactions 
ADD CONSTRAINT point_purchase_transactions_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE affiliate_accounts 
ADD CONSTRAINT affiliate_accounts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE affiliate_contracts 
ADD CONSTRAINT affiliate_contracts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Kiểm tra kết quả
SELECT 
    'MIGRATION COMPLETED' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees,
    (SELECT COUNT(*) FROM users) as users_view_count;

COMMIT;

-- Thông báo hoàn thành
SELECT 'DATABASE MIGRATION COMPLETED SUCCESSFULLY' as final_status;
