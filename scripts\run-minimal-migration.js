const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function runMinimalMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for minimal migration');

    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'minimal-migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting minimal migration (authentication only)...');
    await client.query(sqlContent);
    console.log('✅ Minimal migration completed!');

    // Verification
    const result = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email
      FROM employees
    `);

    console.log('\n📊 Migration Results:');
    console.table(result.rows);

    const authSample = await client.query(`
      SELECT 
        id, email, employee_name, account_status,
        CASE WHEN password IS NOT NULL THEN 'YES' ELSE 'NO' END as has_password
      FROM employees 
      WHERE email IS NOT NULL AND password IS NOT NULL
      LIMIT 5
    `);

    console.log('\n🔐 Ready for Authentication:');
    console.table(authSample.rows);

    await client.end();
    
    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log(`
🎯 MINIMAL MIGRATION SUMMARY:
- ✅ Migrated authentication data (email, password, account_status)
- ✅ Created employees for users without employee records  
- ✅ Updated user_roles foreign keys
- ✅ System ready for EmployeeAuthService

🚀 NEXT STEPS:
1. Test login with EmployeeAuthService
2. Verify authentication endpoints work
3. Test with existing user credentials

🔄 BACKUP CREATED: employees_backup_minimal, users_backup_minimal
    `);

  } catch (error) {
    console.error('\n❌ Minimal migration failed:', error.message);
    console.error('Error details:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  runMinimalMigration();
}

module.exports = { runMinimalMigration };
