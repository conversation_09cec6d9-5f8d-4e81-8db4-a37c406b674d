import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

/**
 * Entity lưu trữ cấu hình Facebook Page
 */
@Entity('facebook_page_configs')
export class FacebookPageConfig {
  /**
   * ID của Facebook Page
   */
  @PrimaryColumn({ name: 'page_id', type: 'varchar' })
  pageId: string;

  /**
   * Tên của Facebook Page
   */
  @Column({ name: 'page_name', type: 'varchar' })
  pageName: string;

  /**
   * Access token của Page
   */
  @Column({ name: 'access_token', type: 'varchar' })
  accessToken: string;

  /**
   * ID của tenant sở hữu Page
   */
  @Column({ name: 'tenant_id', type: 'bigint' })
  tenantId: number;

  /**
   * ID của employee đã tích hợp Page (formerly user_id, now references Employee.id)
   */
  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  /**
   * Ng<PERSON>y kích hoạt webhook
   */
  @Column({ name: 'webhook_enabled_at', type: 'bigint', nullable: true })
  webhookEnabledAt: number | null;

  /**
   * Trạng thái kích hoạt
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Creation timestamp (in milliseconds)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @UpdateDateColumn({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  updatedAt: number;

  /**
   * Deletion timestamp (in milliseconds)
   */
  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt: number | null;
}
