import { Injectable } from '@nestjs/common';
import { KeyResultSupportService } from '@modules/okrs/services';

// Import tool providers
import {
  EmailToolsProvider,
  TodoToolsProvider,
  EmployeeToolsProvider,
  StatisticsToolsProvider,
  OkrToolsProvider,
  ProjectToolsProvider,
  DepartmentToolsProvider,
  AttendanceToolsProvider,
  TodoExtensionToolsProvider,
  TagToolsProvider,
  EventToolsProvider,
  EmployeeUserToolsProvider,
  UserManagementToolsProvider,
  TodoAttachmentToolsProvider,
  TodoCollaboratorToolsProvider,
  TodoCommentToolsProvider,
  DocumentSharingToolsProvider,
  GanttChartToolsProvider,
} from './tools';

// Import reports tools
import { ReportToolsProvider } from '@/modules/reports/services/report-tools.provider';

/**
 * Business Tools Provider cho ERP system
 * Cung cấp tất cả tools liên quan đến business logic
 * Mỗi tool tương ứng với một chức năng cụ thể trong ERP
 */

export interface RunContextShape {
  tenantId: string;
  userId: string;
}

@Injectable()
export class BusinessToolsProvider {
  constructor(
    // Tool providers
    private readonly emailToolsProvider: EmailToolsProvider,
    private readonly todoToolsProvider: TodoToolsProvider,
    private readonly employeeToolsProvider: EmployeeToolsProvider,
    private readonly statisticsToolsProvider: StatisticsToolsProvider,
    private readonly okrToolsProvider: OkrToolsProvider,
    private readonly projectToolsProvider: ProjectToolsProvider,
    private readonly departmentToolsProvider: DepartmentToolsProvider,
    private readonly attendanceToolsProvider: AttendanceToolsProvider,
    private readonly todoExtensionToolsProvider: TodoExtensionToolsProvider,
    private readonly tagToolsProvider: TagToolsProvider,
    private readonly eventToolsProvider: EventToolsProvider,
    private readonly employeeUserToolsProvider: EmployeeUserToolsProvider,
    private readonly userManagementToolsProvider: UserManagementToolsProvider,
    private readonly todoAttachmentToolsProvider: TodoAttachmentToolsProvider,
    private readonly todoCollaboratorToolsProvider: TodoCollaboratorToolsProvider,
    private readonly todoCommentToolsProvider: TodoCommentToolsProvider,
    private readonly documentSharingToolsProvider: DocumentSharingToolsProvider,
    private readonly ganttChartToolsProvider: GanttChartToolsProvider,
    private readonly reportToolsProvider: ReportToolsProvider,

    // Legacy services for backward compatibility
    private readonly keyResultSupportService: KeyResultSupportService,
  ) {}

  /**
   * Khởi tạo và trả về tất cả business tools đã định nghĩa
   * Đây là entry point để sử dụng các tools trong hệ thống
   */
  getEmailTools() {
    return this.emailToolsProvider.getTools();
  }

  getTodoTools() {
    return this.todoToolsProvider.getTools();
  }

  getEmployeeTools() {
    return this.employeeToolsProvider.getTools();
  }

  getStatisticsTools() {
    return this.statisticsToolsProvider.getTools();
  }

  getOkrTools() {
    return this.okrToolsProvider.getTools();
  }

  getProjectTools() {
    return this.projectToolsProvider.getTools();
  }

  getDepartmentTools() {
    return this.departmentToolsProvider.getTools();
  }

  getAttendanceTools() {
    return this.attendanceToolsProvider.getTools();
  }

  getTodoExtensionTools() {
    return this.todoExtensionToolsProvider.getTools();
  }

  getTagTools() {
    return this.tagToolsProvider.getTools();
  }

  getEventTools() {
    return this.eventToolsProvider.getTools();
  }

  getEmployeeUserTools() {
    return this.employeeUserToolsProvider.getTools();
  }

  getUserManagementTools() {
    return this.userManagementToolsProvider.getTools();
  }

  getTodoAttachmentTools() {
    return this.todoAttachmentToolsProvider.getTools();
  }

  getTodoCollaboratorTools() {
    return this.todoCollaboratorToolsProvider.getTools();
  }

  getTodoCommentTools() {
    return this.todoCommentToolsProvider.getTools();
  }

  getDocumentSharingTools() {
    return this.documentSharingToolsProvider.getTools();
  }

  getGanttChartTools() {
    return this.ganttChartToolsProvider.getTools();
  }

  getReportTools() {
    return this.reportToolsProvider.getTools();
  }

  // KeyResultSupport tools
  getKeyResultSupportTools() {
    const { tool } = require('@langchain/core/tools');
    const { z } = require('zod');

    return [
      // Thêm Key Results hỗ trợ
      tool(
        async (_args, config) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const createDto = {
              childIds: _args.childIds,
            };

            const supports = await this.keyResultSupportService.addSupports(
              tenantId,
              _args.parentId,
              createDto,
            );

            return `Đã thêm ${supports.length} Key Results hỗ trợ cho Key Result ID ${_args.parentId}`;
          } catch (error) {
            return `Thêm Key Results hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_key_result_supports',
          description: 'Thêm các Key Results hỗ trợ cho một Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
            childIds: z.array(z.number()).describe('Danh sách ID các Key Results hỗ trợ'),
          }),
        }
      ),

      // Lấy danh sách Key Results hỗ trợ
      tool(
        async (_args, config) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const supports = await this.keyResultSupportService.getSupports(
              tenantId,
              _args.parentId,
            );

            if (supports.length === 0) {
              return `Key Result ID ${_args.parentId} không có Key Results hỗ trợ nào`;
            }

            const supportList = supports.map(s =>
              `- ${s.childKeyResult?.title || 'N/A'} (ID: ${s.childId})`
            ).join('\n');

            return `Key Result ID ${_args.parentId} có ${supports.length} Key Results hỗ trợ:\n${supportList}`;
          } catch (error) {
            return `Lấy danh sách Key Results hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_key_result_supports',
          description: 'Lấy danh sách các Key Results hỗ trợ cho một Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
          }),
        }
      ),

      // Xóa Key Result hỗ trợ
      tool(
        async (_args, config) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const removed = await this.keyResultSupportService.removeSupport(
              tenantId,
              _args.parentId,
              _args.childId,
            );

            if (removed) {
              return `Đã xóa Key Result ID ${_args.childId} khỏi danh sách hỗ trợ của Key Result ID ${_args.parentId}`;
            } else {
              return `Không tìm thấy mối quan hệ hỗ trợ giữa Key Result ${_args.parentId} và ${_args.childId}`;
            }
          } catch (error) {
            return `Xóa Key Result hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'remove_key_result_support',
          description: 'Xóa một Key Result khỏi danh sách hỗ trợ của Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
            childId: z.number().describe('ID của Key Result hỗ trợ cần xóa'),
          }),
        }
      ),
    ];
  }

  /**
   * Lấy tất cả business tools đã định nghĩa
   * Method này sẽ được gọi khi khởi tạo để register tools
   */
  getAllBusinessTools() {
    return [
      ...this.getEmailTools(),
      ...this.getTodoTools(),
      ...this.getEmployeeTools(),
      ...this.getStatisticsTools(),
      ...this.getOkrTools(),
      ...this.getProjectTools(),
      ...this.getDepartmentTools(),
      ...this.getAttendanceTools(),
      ...this.getTodoExtensionTools(),
      ...this.getTagTools(),
      ...this.getEventTools(),
      ...this.getEmployeeUserTools(),
      ...this.getUserManagementTools(),
      ...this.getTodoAttachmentTools(),
      ...this.getTodoCollaboratorTools(),
      ...this.getTodoCommentTools(),
      ...this.getDocumentSharingTools(),
      ...this.getGanttChartTools(),
      ...this.getReportTools(),
      ...this.getKeyResultSupportTools(),
    ];
  }
}
