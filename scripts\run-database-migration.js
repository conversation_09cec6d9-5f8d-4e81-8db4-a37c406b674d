const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Script chạy database migration để gộp Users vào Employees
 * Chạy tất cả migration files theo thứ tự
 */

async function runDatabaseMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for migration');

    // Danh sách migration files theo thứ tự
    const migrationFiles = [
      '001-update-employees-structure.sql',
      '002-migrate-users-to-employees.sql',
      '003-cleanup-users-table.sql'
    ];

    console.log('\n🚀 Starting database migration process...\n');

    // Chạy từng migration file
    for (const fileName of migrationFiles) {
      const filePath = path.join(__dirname, '..', 'database', 'migrations', fileName);
      
      console.log(`📄 Running migration: ${fileName}`);
      
      try {
        // Đọc file SQL
        const sqlContent = fs.readFileSync(filePath, 'utf8');
        
        // Chạy SQL
        await client.query(sqlContent);
        
        console.log(`✅ Completed migration: ${fileName}`);
        
      } catch (error) {
        console.error(`❌ Failed migration: ${fileName}`);
        console.error('Error:', error.message);
        throw error;
      }
    }

    console.log('\n🎉 All migrations completed successfully!');

    // Chạy verification queries
    console.log('\n📊 Running verification checks...');

    // Kiểm tra cấu trúc employees table
    const employeesStructure = await client.query(`
      SELECT 
        column_name, 
        data_type, 
        is_nullable, 
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Employees table structure:');
    console.table(employeesStructure.rows);

    // Kiểm tra dữ liệu
    const dataCheck = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id
      FROM employees
    `);

    console.log('\n📈 Data verification:');
    console.table(dataCheck.rows);

    // Kiểm tra migration history (nếu có)
    try {
      const migrationHistory = await client.query(`
        SELECT * FROM migration_history ORDER BY started_at
      `);
      
      console.log('\n📜 Migration history:');
      console.table(migrationHistory.rows);
    } catch (error) {
      console.log('ℹ️ No migration history table found (normal for some migration methods)');
    }

    // Kiểm tra foreign key constraints
    const foreignKeys = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND ccu.table_name = 'employees'
      ORDER BY tc.table_name, tc.constraint_name
    `);

    console.log('\n🔗 Foreign key constraints to employees:');
    console.table(foreignKeys.rows);

    // Kiểm tra users table status
    try {
      const usersCheck = await client.query('SELECT COUNT(*) as count FROM users');
      console.log(`\n👤 Users table: ${usersCheck.rows[0].count} records (should be view now)`);
    } catch (error) {
      console.log('\n👤 Users table: Not found (successfully removed)');
    }

    await client.end();
    console.log('\n✅ Database migration completed successfully!');

    // Tóm tắt kết quả
    console.log(`
🎯 MIGRATION SUMMARY:
- ✅ Employees table structure updated
- ✅ Authentication fields added to employees
- ✅ Users data migrated to employees
- ✅ Foreign key references updated
- ✅ Users table removed/replaced with view
- ✅ Database ready for new authentication flow

🚀 NEXT STEPS:
1. Test authentication with EmployeeAuthService
2. Verify all API endpoints work correctly
3. Update frontend if needed
4. Remove deprecated UserRepository code
    `);

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error(error.stack);
    
    console.log(`
🔄 ROLLBACK OPTIONS:
1. Restore from backup tables (employees_backup_old, users_backup)
2. Run rollback scripts if available
3. Restore database from full backup
    `);
    
    process.exit(1);
  }
}

// Chạy migration nếu script được gọi trực tiếp
if (require.main === module) {
  console.log('🎯 Starting User-Employee merge migration...');
  runDatabaseMigration();
}

module.exports = { runDatabaseMigration };
