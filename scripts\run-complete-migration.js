const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Script chạy complete migration để gộp Users vào Employees
 */

async function runCompleteMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for migration');

    // Đọc complete migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'complete-migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting complete database migration...');
    console.log('📄 Running complete-migration.sql');

    // Chạy migration
    await client.query(sqlContent);

    console.log('✅ Migration completed successfully!');

    // Verification
    console.log('\n📊 Running verification checks...');

    // Kiểm tra cấu trúc employees table
    const employeesStructure = await client.query(`
      SELECT 
        column_name, 
        data_type, 
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      AND column_name IN ('email', 'password', 'account_status', 'tenant_id', 'employee_code', 'employee_name')
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Key employees table columns:');
    console.table(employeesStructure.rows);

    // Kiểm tra dữ liệu
    const dataCheck = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id,
        COUNT(CASE WHEN employee_code IS NOT NULL THEN 1 END) as has_employee_code
      FROM employees
    `);

    console.log('\n📈 Data verification:');
    console.table(dataCheck.rows);

    // Kiểm tra users view
    try {
      const usersViewCheck = await client.query('SELECT COUNT(*) as count FROM users');
      console.log(`\n👤 Users view: ${usersViewCheck.rows[0].count} records`);
    } catch (error) {
      console.log('\n❌ Users view check failed:', error.message);
    }

    // Kiểm tra sample data
    const sampleData = await client.query(`
      SELECT 
        id,
        email,
        employee_name,
        account_status,
        tenant_id,
        employee_code,
        status
      FROM employees 
      LIMIT 3
    `);

    console.log('\n📋 Sample employee data:');
    console.table(sampleData.rows);

    // Kiểm tra foreign key constraints
    const foreignKeys = await client.query(`
      SELECT 
        tc.table_name,
        tc.constraint_name,
        kcu.column_name
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND kcu.column_name = 'user_id'
      ORDER BY tc.table_name
    `);

    console.log('\n🔗 Foreign key constraints (user_id columns):');
    console.table(foreignKeys.rows);

    await client.end();
    console.log('\n✅ Database migration completed successfully!');

    // Tóm tắt kết quả
    console.log(`
🎯 MIGRATION SUMMARY:
- ✅ Employees table structure updated with authentication fields
- ✅ Users data migrated to employees table
- ✅ Foreign key references updated to point to employees
- ✅ Users table removed and replaced with compatibility view
- ✅ Database ready for EmployeeAuthService

🚀 NEXT STEPS:
1. Test authentication with EmployeeAuthService
2. Verify all API endpoints work correctly
3. Test login flow with existing credentials
4. Update any remaining code references if needed

🔄 BACKUP TABLES CREATED:
- employees_backup_original (original employees data)
- users_backup_original (original users data)
    `);

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Full error:', error);
    
    console.log(`
🔄 ROLLBACK OPTIONS:
1. Restore from backup tables:
   - DROP TABLE employees; 
   - ALTER TABLE employees_backup_original RENAME TO employees;
   - ALTER TABLE users_backup_original RENAME TO users;
2. Restore database from full backup if available
    `);
    
    process.exit(1);
  }
}

// Chạy migration nếu script được gọi trực tiếp
if (require.main === module) {
  console.log('🎯 Starting complete User-Employee merge migration...');
  runCompleteMigration();
}

module.exports = { runCompleteMigration };
