-- =====================================================
-- REAL MIGRATION: Gộp Users vào Employees (dựa trên cấu trúc thực tế)
-- Ngày: 2025-06-18
-- =====================================================

-- Backup dữ liệu
CREATE TABLE IF NOT EXISTS employees_backup_real AS SELECT * FROM employees;
CREATE TABLE IF NOT EXISTS users_backup_real AS SELECT * FROM users;

-- <PERSON><PERSON><PERSON> bảo employees table có email unique constraint
ALTER TABLE employees ADD CONSTRAINT IF NOT EXISTS employees_email_unique UNIQUE (email);

-- Cập nhật employees có email trùng với users
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.status::text = 'active' THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    date_of_birth = u.birth_date,
    gender = CASE 
        WHEN u.gender = 'male' THEN 'male'
        WHEN u.gender = 'female' THEN 'female'
        ELSE 'other'
    END,
    bank_account_number = u.bank_account_number,
    bank_name = u.bank_name,
    tax_code = u.tax_code,
    insurance_number = u.insurance_number,
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Tạo employees mới cho users không có employee
INSERT INTO employees (
    employee_code, employee_name, email, password, account_status, status, tenant_id,
    date_of_birth, gender, bank_account_number, bank_name, tax_code, insurance_number,
    job_title, department_id, hire_date, employment_type, created_at, updated_at
)
SELECT 
    'REDAI' || LPAD((
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    )::text, 3, '0'),
    COALESCE(u.full_name, u.email),
    u.email,
    u.password,
    CASE WHEN u.status::text = 'active' THEN 'ACTIVE' ELSE 'INACTIVE' END,
    CASE WHEN u.status::text = 'active' THEN 'active' ELSE 'inactive' END,
    COALESCE(u.tenant_id, 1),
    u.birth_date,
    CASE 
        WHEN u.gender = 'male' THEN 'male'
        WHEN u.gender = 'female' THEN 'female'
        ELSE 'other'
    END,
    u.bank_account_number,
    u.bank_name,
    u.tax_code,
    u.insurance_number,
    COALESCE(u.position, 'Nhân viên'),
    u.department_id,
    CURRENT_DATE,
    'full_time',
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
);

-- Tạo mapping table để track user_id -> employee_id
CREATE TEMP TABLE user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Cập nhật foreign key references
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ur.user_id = m.user_id;

-- Cập nhật business_info (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_info') THEN
        UPDATE business_info bi
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE bi.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật two_factor_auth (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'two_factor_auth') THEN
        UPDATE two_factor_auth tfa
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE tfa.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật device_info (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'device_info') THEN
        UPDATE device_info di
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE di.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật auth_verification_logs (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'auth_verification_logs') THEN
        UPDATE auth_verification_logs avl
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE avl.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật point_purchase_transactions (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'point_purchase_transactions') THEN
        UPDATE point_purchase_transactions ppt
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE ppt.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật affiliate_accounts (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'affiliate_accounts') THEN
        UPDATE affiliate_accounts aa
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE aa.user_id = m.user_id;
    END IF;
END $$;

-- Cập nhật affiliate_contracts (nếu tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'affiliate_contracts') THEN
        UPDATE affiliate_contracts ac
        SET user_id = m.employee_id
        FROM user_employee_mapping m
        WHERE ac.user_id = m.user_id;
    END IF;
END $$;

-- Thêm missing column nếu cần
ALTER TABLE employees ADD COLUMN IF NOT EXISTS tax_code VARCHAR(50);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS insurance_number VARCHAR(50);

-- Tạo indexes cho performance
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_email ON employees(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_employees_account_status ON employees(account_status);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX IF NOT EXISTS idx_employees_employee_code ON employees(employee_code);

-- Tạo constraints
ALTER TABLE employees 
ADD CONSTRAINT IF NOT EXISTS employees_account_status_check 
CHECK (account_status IN ('ACTIVE', 'INACTIVE', 'PENDING'));

-- Kiểm tra kết quả
SELECT 
    'REAL MIGRATION COMPLETED' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees,
    (SELECT COUNT(*) FROM employees WHERE password IS NOT NULL) as employees_with_password;

SELECT 'REAL MIGRATION COMPLETED SUCCESSFULLY' as final_status;
