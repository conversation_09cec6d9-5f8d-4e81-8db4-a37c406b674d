import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Import modules
import { AuthModule } from '@/modules/auth/auth.module';
import { HrmModule } from '@/modules/hrm/hrm.module';
import { OkrsModule } from '@/modules/okrs/okrs.module';
import { TodolistsModule } from '@/modules/todolists/todolists.module';

// Import controllers
import { ReportsController } from './controllers/reports.controller';
import { HRReportsController } from './controllers/hr-reports.controller';

// Import services
import { SchemaExcelGeneratorService } from './services/schema-excel-generator.service';
import { ReportTemplateService } from './services/report-template.service';
import { HRReportsService } from './services/hr-reports.service';
import { AttendanceReportsService } from './services/attendance-reports.service';

// Import shared services
import { S3Service } from '@shared/services/s3.service';

/**
 * Module quản lý hệ thống báo cáo với khả năng xuất Excel từ JSON schema
 */
@Global()
@Module({
  imports: [
    // Import các module cần thiết
    AuthModule,
    HrmModule,
    OkrsModule,
    TodolistsModule,
  ],
  controllers: [
    ReportsController,
    HRReportsController,
  ],
  providers: [
    // Core services
    SchemaExcelGeneratorService,
    ReportTemplateService,
    
    // Specialized report services
    HRReportsService,
    AttendanceReportsService,
    
    // Shared services
    S3Service,
  ],
  exports: [
    // Export services để có thể sử dụng ở module khác
    SchemaExcelGeneratorService,
    ReportTemplateService,
    HRReportsService,
    AttendanceReportsService,
  ],
})
export class ReportsModule {}
