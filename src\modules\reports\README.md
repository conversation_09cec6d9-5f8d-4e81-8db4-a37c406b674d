# 📊 Reports Module - <PERSON>ệ Thống Báo Cáo Excel

Module Reports cung cấp khả năng tạo báo cáo Excel từ JSON schema với đầy đủ tính năng styling, charts và template system.

## 🎯 Tính Năng Chính

### ✅ 1. **Schema-Based Excel Generation**
- Tạo Excel từ JSON schema linh hoạt
- Hỗ trợ multiple sheets trong một file
- Styling tùy chỉnh (fonts, colors, borders)
- Auto-fit columns và formatting

### ✅ 2. **Template System**
- Template có sẵn cho các loại báo cáo
- Customizable headers và styling
- Dynamic columns dựa trên data
- Logo công ty và thông tin metadata

### ✅ 3. **Report Types**
- **HR Reports**: Danh sách nhân viên, thống kê nhân sự
- **Attendance Reports**: Chấm công chi tiết, tổng hợp chấm công
- **OKR Reports**: Tiến độ OKR (đang phát triển)
- **Project Reports**: Tiến độ dự án (đang phát triển)

### ✅ 4. **Chat AI Integration**
- Tạo báo cáo qua chat AI
- Commands đơn giản và trực quan
- Download links tự động
- Thống kê tóm tắt

### ✅ 5. **Cloud Storage Integration**
- Upload tự động lên S3
- Temporary download URLs (24h)
- File management và cleanup

## 🏗️ Cấu Trúc Module

```
src/modules/reports/
├── controllers/           # API Controllers
│   ├── reports.controller.ts
│   └── hr-reports.controller.ts
├── services/             # Business Logic
│   ├── schema-excel-generator.service.ts
│   ├── report-template.service.ts
│   ├── hr-reports.service.ts
│   ├── attendance-reports.service.ts
│   └── report-tools.provider.ts
├── dto/                  # Data Transfer Objects
│   ├── base-report.dto.ts
│   └── hr-reports.dto.ts
├── interfaces/           # TypeScript Interfaces
│   ├── excel-schema.interface.ts
│   └── report.interface.ts
├── enums/               # Enums
│   └── report-type.enum.ts
└── reports.module.ts    # Module Definition
```

## 🚀 Cách Sử Dụng

### 1. **Qua REST API**

```typescript
// Tạo báo cáo danh sách nhân viên
POST /api/v1/reports/hr/employees
{
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "filters": {
    "departmentIds": [1, 2, 3],
    "status": ["active"]
  },
  "options": {
    "includeCharts": true,
    "includeStatistics": true,
    "format": "xlsx"
  }
}
```

### 2. **Qua Chat AI**

```
Tạo báo cáo nhân viên phòng IT tháng này
Xuất báo cáo chấm công từ 1/12 đến 31/12
Tạo thống kê nhân sự có biểu đồ
```

### 3. **Programmatic Usage**

```typescript
import { SchemaExcelGeneratorService, ReportTemplateService } from '@/modules/reports';

// Inject services
constructor(
  private readonly excelGenerator: SchemaExcelGeneratorService,
  private readonly templateService: ReportTemplateService,
) {}

// Generate report
async generateReport() {
  const schema = this.templateService.getTemplateByType(ReportType.HR_EMPLOYEES);
  const result = await this.excelGenerator.generateFromSchema(data, schema, options);
  return result;
}
```

## 📋 JSON Schema Format

```typescript
interface ExcelReportSchema {
  metadata: {
    title: string;
    description?: string;
    author?: string;
    company?: string;
  };
  sheets: ExcelSheetSchema[];
}

interface ExcelSheetSchema {
  name: string;
  columns: ExcelColumnSchema[];
  styling?: ExcelStylingSchema;
  charts?: ExcelChartSchema[];
}

interface ExcelColumnSchema {
  key: string;
  header: string;
  width?: number;
  type: 'string' | 'number' | 'date' | 'currency' | 'percentage';
  format?: string;
  formula?: string;
}
```

## 🎨 Styling Options

```typescript
interface ExcelStylingSchema {
  header: {
    font: { bold: true, color: 'FFFFFF', size: 12 };
    fill: { type: 'pattern', pattern: 'solid', fgColor: '4472C4' };
    alignment: { horizontal: 'center', vertical: 'middle' };
  };
  data: {
    font: { size: 11 };
    alignment: { vertical: 'middle' };
  };
  alternatingRows: true;
  alternatingRowColor: 'FFF2F2F2';
  freezeHeader: true;
  autoFilter: true;
}
```

## 📊 Chart Support

```typescript
interface ExcelChartSchema {
  type: 'pie' | 'column' | 'bar' | 'line';
  title: string;
  position: string; // e.g., "H2"
  size: { width: number; height: number };
  dataRange: string;
  options: {
    showLegend: boolean;
    showDataLabels: boolean;
    colors?: string[];
  };
}
```

## 🔧 Configuration

### Environment Variables
```env
# S3 Configuration (for file storage)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=ap-southeast-1
AWS_S3_BUCKET=your-bucket-name
```

### Dependencies
```json
{
  "exceljs": "^4.4.0",
  "@types/exceljs": "^1.3.0",
  "moment": "^2.29.4",
  "lodash": "^4.17.21"
}
```

## 🎯 Chat Commands

### Báo Cáo Nhân Sự
- `Tạo báo cáo nhân viên`
- `Xuất danh sách nhân viên phòng IT`
- `Báo cáo thống kê nhân sự có biểu đồ`

### Báo Cáo Chấm Công
- `Tạo báo cáo chấm công tháng này`
- `Xuất chấm công chi tiết từ 1/12 đến 31/12`
- `Báo cáo tổng hợp chấm công phòng Sales`

### Lấy Thông Tin
- `Danh sách loại báo cáo`
- `Các báo cáo có sẵn`

## 📈 Response Format

```typescript
interface ReportResponse {
  success: boolean;
  data: {
    fileName: string;
    downloadUrl: string;
    fileSize: number;
    generatedAt: string;
    expiresAt: string;
  };
  statistics?: {
    totalEmployees: number;
    activeEmployees: number;
    // ... other stats
  };
  message: string;
}
```

## 🔒 Security & Permissions

- Sử dụng JWT authentication
- Tenant isolation tự động
- Role-based access control
- Audit logging cho việc export báo cáo
- File URLs có thời hạn (24h)

## 🚀 Roadmap

### Phase 2 (Upcoming)
- [ ] OKR Reports implementation
- [ ] Project Reports implementation
- [ ] PDF export support
- [ ] Email delivery
- [ ] Scheduled reports
- [ ] Custom report builder UI

### Phase 3 (Future)
- [ ] Dashboard integration
- [ ] Real-time data refresh
- [ ] Advanced charting
- [ ] Report sharing & collaboration
- [ ] Mobile-optimized reports

## 🐛 Troubleshooting

### Common Issues

1. **File generation fails**
   - Check data format và schema compatibility
   - Verify S3 credentials và permissions

2. **Charts not displaying**
   - Ensure `includeCharts: true` in options
   - Check data range validity

3. **Download URL expired**
   - URLs expire after 24 hours
   - Regenerate report for new URL

### Debug Mode
```typescript
// Enable debug logging
process.env.ENABLE_REPORTS_DEBUG = 'true';
```

## 📞 Support

Để được hỗ trợ, vui lòng:
1. Check logs trong console
2. Verify request format
3. Contact development team với error details

---

**Developed by RedAI Team** 🚀
