/**
 * Interface định nghĩa schema cho việc tạo báo cáo Excel
 */

export interface ExcelReportSchema {
  metadata: ExcelMetadata;
  sheets: ExcelSheetSchema[];
}

export interface ExcelMetadata {
  title: string;
  description?: string;
  author?: string;
  company?: string;
  category?: string;
  keywords?: string[];
}

export interface ExcelSheetSchema {
  name: string;
  columns: ExcelColumnSchema[];
  styling?: ExcelStylingSchema;
  charts?: ExcelChartSchema[];
  filters?: ExcelFilterSchema[];
  summary?: ExcelSummarySchema;
}

export interface ExcelColumnSchema {
  key: string;
  header: string;
  width?: number;
  type: ExcelColumnType;
  format?: string;
  formula?: string;
  validation?: ExcelValidationSchema;
  hidden?: boolean;
}

export interface ExcelStylingSchema {
  header: ExcelCellStyle;
  data?: ExcelCellStyle;
  alternatingRows?: boolean;
  alternatingRowColor?: string;
  freezeHeader?: boolean;
  autoFilter?: boolean;
}

export interface ExcelCellStyle {
  font?: {
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    color?: string;
    size?: number;
    name?: string;
  };
  fill?: {
    type: 'pattern' | 'gradient';
    pattern?: string;
    fgColor?: string;
    bgColor?: string;
  };
  border?: {
    top?: ExcelBorderStyle;
    left?: ExcelBorderStyle;
    bottom?: ExcelBorderStyle;
    right?: ExcelBorderStyle;
  };
  alignment?: {
    horizontal?: 'left' | 'center' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
    wrapText?: boolean;
  };
  numberFormat?: string;
}

export interface ExcelBorderStyle {
  style: 'thin' | 'medium' | 'thick' | 'dotted' | 'dashed';
  color?: string;
}

export interface ExcelChartSchema {
  type: ExcelChartType;
  title: string;
  position: string; // Ví dụ: "H2"
  size?: {
    width: number;
    height: number;
  };
  dataRange: string;
  categoryRange?: string;
  options?: {
    showLegend?: boolean;
    showDataLabels?: boolean;
    colors?: string[];
  };
}

export interface ExcelFilterSchema {
  column: string;
  type: 'autoFilter' | 'customFilter';
  criteria?: any;
}

export interface ExcelSummarySchema {
  position: 'top' | 'bottom';
  calculations: ExcelCalculationSchema[];
}

export interface ExcelCalculationSchema {
  label: string;
  formula: string;
  format?: string;
  style?: ExcelCellStyle;
}

export interface ExcelValidationSchema {
  type: 'list' | 'whole' | 'decimal' | 'date' | 'time' | 'textLength' | 'custom';
  criteria: any;
  errorMessage?: string;
  showErrorMessage?: boolean;
}

export type ExcelColumnType = 
  | 'string' 
  | 'number' 
  | 'date' 
  | 'datetime'
  | 'time'
  | 'boolean' 
  | 'currency'
  | 'percentage'
  | 'email'
  | 'phone'
  | 'url';

export type ExcelChartType = 
  | 'column'
  | 'bar'
  | 'line'
  | 'pie'
  | 'doughnut'
  | 'area'
  | 'scatter'
  | 'bubble';

/**
 * Interface cho response khi generate Excel
 */
export interface ExcelGenerationResult {
  success: boolean;
  fileName: string;
  fileSize: number;
  downloadUrl?: string;
  buffer?: Buffer;
  generatedAt: Date;
  metadata: ExcelMetadata;
}

/**
 * Interface cho options khi generate Excel
 */
export interface ExcelGenerationOptions {
  includeCharts: boolean;
  includeStatistics: boolean;
  format: 'xlsx' | 'csv' | 'pdf';
  password?: string;
  watermark?: string;
  compression?: boolean;
}
