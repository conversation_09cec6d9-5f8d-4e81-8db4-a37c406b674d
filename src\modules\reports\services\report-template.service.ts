import { Injectable, Logger } from '@nestjs/common';
import { ExcelReportSchema } from '../interfaces';
import { ReportType } from '../enums';

/**
 * Service quản lý các template báo cáo có sẵn
 */
@Injectable()
export class ReportTemplateService {
  private readonly logger = new Logger(ReportTemplateService.name);

  /**
   * Lấy template schema theo loại báo cáo
   */
  getTemplateByType(reportType: ReportType): ExcelReportSchema {
    switch (reportType) {
      case ReportType.HR_EMPLOYEES:
        return this.getHREmployeesTemplate();
      case ReportType.HR_STATISTICS:
        return this.getHRStatisticsTemplate();
      case ReportType.ATTENDANCE_DETAILS:
        return this.getAttendanceDetailsTemplate();
      case ReportType.ATTENDANCE_SUMMARY:
        return this.getAttendanceSummaryTemplate();
      case ReportType.OKR_PROGRESS:
        return this.getOKRProgressTemplate();
      case ReportType.OKR_STATISTICS:
        return this.getOKRStatisticsTemplate();
      case ReportType.PROJECT_PROGRESS:
        return this.getProjectProgressTemplate();
      case ReportType.TODO_SUMMARY:
        return this.getTodoSummaryTemplate();
      default:
        throw new Error(`Template không tồn tại cho loại báo cáo: ${reportType}`);
    }
  }

  /**
   * Template báo cáo danh sách nhân viên
   */
  private getHREmployeesTemplate(): ExcelReportSchema {
    return {
      metadata: {
        title: 'Báo Cáo Danh Sách Nhân Viên',
        description: 'Danh sách chi tiết thông tin nhân viên trong công ty',
        author: 'RedAI System',
        company: 'RedAI',
        category: 'HR',
        keywords: ['nhân viên', 'danh sách', 'HR']
      },
      sheets: [
        {
          name: 'Danh Sách Nhân Viên',
          columns: [
            {
              key: 'employeeCode',
              header: 'Mã NV',
              width: 12,
              type: 'string'
            },
            {
              key: 'employeeName',
              header: 'Họ Tên',
              width: 25,
              type: 'string'
            },
            {
              key: 'email',
              header: 'Email',
              width: 30,
              type: 'email'
            },
            {
              key: 'departmentName',
              header: 'Phòng Ban',
              width: 20,
              type: 'string'
            },
            {
              key: 'position',
              header: 'Chức Vụ',
              width: 20,
              type: 'string'
            },
            {
              key: 'hireDate',
              header: 'Ngày Vào Làm',
              width: 15,
              type: 'date',
              format: 'dd/mm/yyyy'
            },
            {
              key: 'employmentType',
              header: 'Loại Hợp Đồng',
              width: 15,
              type: 'string'
            },
            {
              key: 'status',
              header: 'Trạng Thái',
              width: 12,
              type: 'string'
            }
          ],
          styling: {
            header: {
              font: { bold: true, color: 'FFFFFF', size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: '4472C4' },
              alignment: { horizontal: 'center', vertical: 'middle' }
            },
            data: {
              font: { size: 11 },
              alignment: { vertical: 'middle' }
            },
            alternatingRows: true,
            alternatingRowColor: 'FFF2F2F2',
            freezeHeader: true,
            autoFilter: true
          },
          charts: [
            {
              type: 'pie',
              title: 'Phân Bố Theo Phòng Ban',
              position: 'J2',
              size: { width: 400, height: 300 },
              dataRange: 'D2:D1000',
              options: {
                showLegend: true,
                showDataLabels: true
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Template báo cáo thống kê nhân sự
   */
  private getHRStatisticsTemplate(): ExcelReportSchema {
    return {
      metadata: {
        title: 'Báo Cáo Thống Kê Nhân Sự',
        description: 'Thống kê tổng quan về nhân sự công ty',
        author: 'RedAI System',
        company: 'RedAI',
        category: 'HR'
      },
      sheets: [
        {
          name: 'Thống Kê Tổng Quan',
          columns: [
            {
              key: 'metric',
              header: 'Chỉ Số',
              width: 25,
              type: 'string'
            },
            {
              key: 'value',
              header: 'Giá Trị',
              width: 15,
              type: 'number'
            },
            {
              key: 'percentage',
              header: 'Tỷ Lệ (%)',
              width: 12,
              type: 'percentage',
              format: '0.00%'
            }
          ],
          styling: {
            header: {
              font: { bold: true, color: 'FFFFFF', size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: '70AD47' }
            },
            alternatingRows: true,
            freezeHeader: true
          }
        },
        {
          name: 'Phân Bố Theo Phòng Ban',
          columns: [
            {
              key: 'departmentName',
              header: 'Phòng Ban',
              width: 25,
              type: 'string'
            },
            {
              key: 'employeeCount',
              header: 'Số Lượng NV',
              width: 15,
              type: 'number'
            },
            {
              key: 'percentage',
              header: 'Tỷ Lệ (%)',
              width: 12,
              type: 'percentage'
            }
          ],
          styling: {
            header: {
              font: { bold: true, color: 'FFFFFF', size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: 'FFC000' }
            },
            alternatingRows: true
          },
          charts: [
            {
              type: 'column',
              title: 'Số Lượng Nhân Viên Theo Phòng Ban',
              position: 'E2',
              dataRange: 'A2:B20'
            }
          ]
        }
      ]
    };
  }

  /**
   * Template báo cáo chấm công chi tiết
   */
  private getAttendanceDetailsTemplate(): ExcelReportSchema {
    return {
      metadata: {
        title: 'Báo Cáo Chấm Công Chi Tiết',
        description: 'Chi tiết chấm công của nhân viên theo khoảng thời gian',
        author: 'RedAI System',
        category: 'Attendance'
      },
      sheets: [
        {
          name: 'Chi Tiết Chấm Công',
          columns: [
            {
              key: 'employeeCode',
              header: 'Mã NV',
              width: 12,
              type: 'string'
            },
            {
              key: 'employeeName',
              header: 'Họ Tên',
              width: 25,
              type: 'string'
            },
            {
              key: 'workDate',
              header: 'Ngày Làm',
              width: 12,
              type: 'date'
            },
            {
              key: 'checkInTime',
              header: 'Giờ Vào',
              width: 12,
              type: 'datetime',
              format: 'hh:mm'
            },
            {
              key: 'checkOutTime',
              header: 'Giờ Ra',
              width: 12,
              type: 'datetime',
              format: 'hh:mm'
            },
            {
              key: 'workHours',
              header: 'Giờ Làm (h)',
              width: 12,
              type: 'number',
              format: '0.00'
            },
            {
              key: 'overtimeHours',
              header: 'Tăng Ca (h)',
              width: 12,
              type: 'number',
              format: '0.00'
            },
            {
              key: 'status',
              header: 'Trạng Thái',
              width: 15,
              type: 'string'
            }
          ],
          styling: {
            header: {
              font: { bold: true, color: 'FFFFFF', size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: 'E74C3C' }
            },
            alternatingRows: true,
            freezeHeader: true,
            autoFilter: true
          }
        }
      ]
    };
  }

  /**
   * Template báo cáo OKR progress
   */
  private getOKRProgressTemplate(): ExcelReportSchema {
    return {
      metadata: {
        title: 'Báo Cáo Tiến Độ OKR',
        description: 'Tiến độ thực hiện các mục tiêu OKR',
        author: 'RedAI System',
        category: 'OKR'
      },
      sheets: [
        {
          name: 'Tiến Độ OKR',
          columns: [
            {
              key: 'objectiveTitle',
              header: 'Mục Tiêu',
              width: 30,
              type: 'string'
            },
            {
              key: 'ownerName',
              header: 'Người Phụ Trách',
              width: 20,
              type: 'string'
            },
            {
              key: 'departmentName',
              header: 'Phòng Ban',
              width: 20,
              type: 'string'
            },
            {
              key: 'type',
              header: 'Loại',
              width: 12,
              type: 'string'
            },
            {
              key: 'progress',
              header: 'Tiến Độ (%)',
              width: 12,
              type: 'percentage'
            },
            {
              key: 'status',
              header: 'Trạng Thái',
              width: 15,
              type: 'string'
            },
            {
              key: 'keyResultsCount',
              header: 'Số KR',
              width: 10,
              type: 'number'
            }
          ],
          styling: {
            header: {
              font: { bold: true, color: 'FFFFFF', size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: '9B59B6' }
            },
            alternatingRows: true,
            freezeHeader: true
          }
        }
      ]
    };
  }

  /**
   * Các template khác sẽ được implement tương tự
   */
  private getAttendanceSummaryTemplate(): ExcelReportSchema {
    // Implementation tương tự
    return this.getAttendanceDetailsTemplate(); // Placeholder
  }

  private getOKRStatisticsTemplate(): ExcelReportSchema {
    // Implementation tương tự
    return this.getOKRProgressTemplate(); // Placeholder
  }

  private getProjectProgressTemplate(): ExcelReportSchema {
    // Implementation tương tự
    return this.getOKRProgressTemplate(); // Placeholder
  }

  private getTodoSummaryTemplate(): ExcelReportSchema {
    // Implementation tương tự
    return this.getOKRProgressTemplate(); // Placeholder
  }
}
