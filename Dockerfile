# Sử dụng Node.js 20 Alpine cho image nhỏ gọn
FROM node:20-alpine AS base

# Cài đặt các dependencies cần thiết cho Alpine
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Tạo user non-root để chạy ứng dụng
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

WORKDIR /app

# Sao chép package files
COPY package*.json ./

# Stage cho development dependencies
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# Stage cho build
FROM base AS build
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Stage production cuối cùng
FROM base AS production

# Sao chép node_modules từ deps stage
COPY --from=deps --chown=nestjs:nodejs /app/node_modules ./node_modules

# Sao ché<PERSON> built application từ build stage
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist

# Sao chép package.json để có thông tin về scripts
COPY --chown=nestjs:nodejs package*.json ./

# Chuyển sang user non-root
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node --version || exit 1

# Chạy ứng dụng
CMD ["node", "dist/main.js"]