import { Injectable } from '@nestjs/common';
import { IntentCategory } from './base-agent.interface';
import { BaseReactAgent } from './base-react-agent';

// Import Reports tool provider
import { ReportToolsProvider } from '@/modules/reports/services/report-tools.provider';

/**
 * Reports Agent - Chuyên xử lý các yêu cầu liên quan đến báo cáo Excel
 */
@Injectable()
export class ReportsAgentService extends BaseReactAgent {
  readonly name = 'reports-agent';
  readonly description = 'Agent chuyên tạo và xuất báo cáo Excel từ dữ liệu hệ thống';
  readonly priority = 8;
  readonly keywords = [
    'báo cáo', 'report', 'excel', 'xuất', 'export', 'tải về', 'download',
    'file', 'bảng tính', 'spreadsheet', 'danh sách', 'thống kê',
    'nhân viên', 'employee', 'chấm công', 'attendance', 'hr',
    'tạo báo cáo', 'xuất báo cáo', 'tạo file', 'xuất file'
  ];

  constructor(
    private readonly reportToolsProvider: ReportToolsProvider,
  ) {
    super();
  }

  /**
   * Kiểm tra xem có match với intent category không
   */
  protected matchesIntentCategory(category: string): boolean {
    return category === IntentCategory.ANALYTICS; // Sử dụng ANALYTICS category
  }

  /**
   * Tính bonus score cho Reports patterns
   */
  protected calculateBonusScore(message: string, baseScore: number): number {
    let bonus = 0;

    // Bonus cao cho từ khóa báo cáo
    if (message.includes('báo cáo') || message.includes('report')) {
      bonus += 0.3;
    }

    // Bonus cao cho từ khóa xuất Excel
    if (message.includes('excel') || message.includes('xuất') || message.includes('export')) {
      bonus += 0.4;
    }

    // Bonus cho từ khóa tải về
    if (message.includes('tải về') || message.includes('download') || message.includes('file')) {
      bonus += 0.2;
    }

    // Bonus cho loại báo cáo cụ thể
    if (message.includes('nhân viên') || message.includes('employee')) {
      bonus += 0.15;
    }

    if (message.includes('chấm công') || message.includes('attendance')) {
      bonus += 0.15;
    }

    if (message.includes('thống kê') || message.includes('statistics')) {
      bonus += 0.1;
    }

    // Bonus cho action words
    if (message.includes('tạo') || message.includes('create') || message.includes('generate')) {
      bonus += 0.1;
    }

    return Math.min(baseScore + bonus, 1);
  }

  /**
   * Lấy tất cả tools của Reports
   */
  getTools(): any[] {
    return [
      ...this.reportToolsProvider.getTools(),
    ];
  }

  /**
   * Tạo system prompt cho Reports Agent
   */
  createSystemPrompt(): string {
    return `Bạn là Reports Agent - chuyên gia tạo báo cáo Excel của hệ thống ERP RedAI.

CHỨC NĂNG CHÍNH:
- Tạo báo cáo Excel từ dữ liệu hệ thống
- Xuất danh sách nhân viên với styling đẹp
- Tạo báo cáo chấm công chi tiết và tổng hợp
- Xuất thống kê nhân sự với biểu đồ
- Cung cấp download links có thời hạn

CÁC LOẠI BÁO CÁO AVAILABLE:
📊 **Báo cáo Nhân sự:**
- Danh sách nhân viên (create_hr_employees_report)
- Thống kê nhân sự (create_hr_statistics_report)

⏰ **Báo cáo Chấm công:**
- Chấm công chi tiết (create_attendance_details_report)
- Tổng hợp chấm công (create_attendance_summary_report)

🎯 **Báo cáo OKR:** (Đang phát triển)
📋 **Báo cáo Dự án:** (Đang phát triển)

NGUYÊN TẮC LÀM VIỆC:
1. Luôn đảm bảo tenant isolation - chỉ xuất dữ liệu của tenant hiện tại
2. Tạo file Excel với styling đẹp, professional
3. Bao gồm charts và visualization phù hợp
4. Cung cấp thống kê tóm tắt cùng với download link
5. File có thời hạn 24 giờ, sau đó tự động xóa
6. Hỗ trợ các bộ lọc linh hoạt theo yêu cầu

CÁCH XỬ LÝ YÊU CẦU:
1. Xác định loại báo cáo user muốn tạo
2. Hỏi rõ khoảng thời gian (nếu cần)
3. Xác nhận các bộ lọc (phòng ban, nhân viên, trạng thái)
4. Tạo báo cáo Excel với styling và charts
5. Cung cấp download link và thống kê tóm tắt

TOOLS AVAILABLE:
- create_hr_employees_report: Tạo báo cáo danh sách nhân viên
- create_hr_statistics_report: Tạo báo cáo thống kê nhân sự
- create_attendance_details_report: Tạo báo cáo chấm công chi tiết
- create_attendance_summary_report: Tạo báo cáo tổng hợp chấm công
- get_available_report_types: Lấy danh sách loại báo cáo

RESPONSE FORMAT:
- Luôn cung cấp download link rõ ràng
- Kèm theo thống kê tóm tắt
- Thông báo thời hạn file (24h)
- Hướng dẫn cách sử dụng file Excel

EXAMPLES:
User: "Tạo báo cáo nhân viên phòng IT"
→ Sử dụng create_hr_employees_report với filter departmentIds

User: "Xuất chấm công tháng này"
→ Sử dụng create_attendance_details_report với dateRange tháng hiện tại

User: "Báo cáo thống kê nhân sự có biểu đồ"
→ Sử dụng create_hr_statistics_report với includeCharts=true

Hãy giúp user tạo báo cáo Excel chuyên nghiệp và hữu ích!`;
  }
}
