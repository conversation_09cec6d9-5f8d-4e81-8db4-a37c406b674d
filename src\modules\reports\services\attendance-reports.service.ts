import { Injectable, Logger } from '@nestjs/common';
import { AttendanceRepository } from '@/modules/hrm/attendance-management/repositories/attendance.repository';
import { EmployeeRepository } from '@/modules/hrm/employees/repositories/employee.repository';
import { ReportData, ReportFilters, DateRange } from '../interfaces';
import * as moment from 'moment';

/**
 * Service xử lý các báo cáo liên quan đến chấm công
 */
@Injectable()
export class AttendanceReportsService {
  private readonly logger = new Logger(AttendanceReportsService.name);

  constructor(
    private readonly attendanceRepository: AttendanceRepository,
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  /**
   * Lấy dữ liệu báo cáo chấm công chi tiết
   */
  async getAttendanceDetailsReportData(
    tenantId: number,
    filters: ReportFilters,
    dateRange: DateRange
  ): Promise<ReportData> {
    try {
      this.logger.log(`L<PERSON>y dữ liệu báo cáo chấm công chi tiết cho tenant: ${tenantId}`);

      // Lấy dữ liệu chấm công trong khoảng thời gian
      const attendances = await this.attendanceRepository.findAttendanceForReport(
        tenantId,
        filters,
        dateRange
      );

      // Lấy thông tin nhân viên
      const employeeIds = [...new Set(attendances.map(att => att.employeeId))];
      const employees = await this.employeeRepository.findByIds(tenantId, employeeIds);
      const employeeMap = new Map(employees.map(emp => [emp.id, emp]));

      // Transform data cho báo cáo
      const reportItems = attendances.map(att => {
        const employee = employeeMap.get(att.employeeId);
        return {
          employeeCode: employee?.employeeCode || 'N/A',
          employeeName: employee?.employeeName || 'N/A',
          workDate: att.workDate,
          checkInTime: att.checkInTime ? this.formatTime(att.checkInTime) : null,
          checkOutTime: att.checkOutTime ? this.formatTime(att.checkOutTime) : null,
          workHours: att.workHours ? (att.workHours / 60).toFixed(2) : 0, // Convert minutes to hours
          overtimeHours: att.overtimeHours ? (att.overtimeHours / 60).toFixed(2) : 0,
          breakTime: att.breakTime ? (att.breakTime / 60).toFixed(2) : 0,
          status: this.formatAttendanceStatus(att.status),
          isLate: att.checkInTime ? this.isLateCheckIn(att.checkInTime) : false,
          isEarlyLeave: att.checkOutTime ? this.isEarlyCheckOut(att.checkOutTime) : false,
          location: att.checkInLocation || 'N/A'
        };
      });

      // Tính toán summary
      const summary = this.calculateAttendanceSummary(reportItems);

      return {
        items: reportItems,
        summary,
        metadata: {
          generatedBy: 'AttendanceReportsService',
          generatedAt: new Date(),
          dataSource: 'attendances',
          version: '1.0',
          filters,
          dateRange
        }
      };

    } catch (error) {
      this.logger.error(`Lỗi lấy dữ liệu báo cáo chấm công: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy dữ liệu báo cáo chấm công: ${error.message}`);
    }
  }

  /**
   * Lấy dữ liệu báo cáo tổng hợp chấm công
   */
  async getAttendanceSummaryReportData(
    tenantId: number,
    filters: ReportFilters,
    dateRange: DateRange
  ): Promise<ReportData> {
    try {
      this.logger.log(`Lấy dữ liệu tổng hợp chấm công cho tenant: ${tenantId}`);

      // Lấy dữ liệu chấm công
      const attendances = await this.attendanceRepository.findAttendanceForReport(
        tenantId,
        filters,
        dateRange
      );

      // Lấy thông tin nhân viên
      const employeeIds = [...new Set(attendances.map(att => att.employeeId))];
      const employees = await this.employeeRepository.findByIds(tenantId, employeeIds);
      const employeeMap = new Map(employees.map(emp => [emp.id, emp]));

      // Group by employee và tính toán summary
      const employeeSummary = this.groupAttendanceByEmployee(attendances, employeeMap);

      // Transform data cho báo cáo
      const reportItems = employeeSummary.map(summary => ({
        employeeCode: summary.employeeCode,
        employeeName: summary.employeeName,
        totalWorkDays: summary.totalWorkDays,
        totalWorkHours: summary.totalWorkHours.toFixed(2),
        totalOvertimeHours: summary.totalOvertimeHours.toFixed(2),
        lateDays: summary.lateDays,
        earlyLeaveDays: summary.earlyLeaveDays,
        absentDays: summary.absentDays,
        averageWorkHours: summary.averageWorkHours.toFixed(2),
        attendanceRate: (summary.attendanceRate * 100).toFixed(1) + '%'
      }));

      // Tính toán summary tổng
      const overallSummary = this.calculateOverallAttendanceSummary(employeeSummary);

      return {
        items: reportItems,
        summary: overallSummary,
        metadata: {
          generatedBy: 'AttendanceReportsService',
          generatedAt: new Date(),
          dataSource: 'attendance_summary',
          version: '1.0',
          filters,
          dateRange
        }
      };

    } catch (error) {
      this.logger.error(`Lỗi lấy dữ liệu tổng hợp chấm công: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy dữ liệu tổng hợp chấm công: ${error.message}`);
    }
  }

  /**
   * Format time từ timestamp
   */
  private formatTime(timestamp: number): string {
    return moment(timestamp).format('HH:mm');
  }

  /**
   * Check if check-in is late (after 8:30 AM)
   */
  private isLateCheckIn(checkInTime: number): boolean {
    if (!checkInTime) return false;
    const checkIn = moment(checkInTime);
    const lateThreshold = moment(checkInTime).startOf('day').add(8, 'hours').add(30, 'minutes');
    return checkIn.isAfter(lateThreshold);
  }

  /**
   * Check if check-out is early (before 5:30 PM)
   */
  private isEarlyCheckOut(checkOutTime: number): boolean {
    if (!checkOutTime) return false;
    const checkOut = moment(checkOutTime);
    const earlyThreshold = moment(checkOutTime).startOf('day').add(17, 'hours').add(30, 'minutes');
    return checkOut.isBefore(earlyThreshold);
  }

  /**
   * Format attendance status
   */
  private formatAttendanceStatus(status: string): string {
    const statusMap = {
      'present': 'Có mặt',
      'absent': 'Vắng mặt',
      'late': 'Đi muộn',
      'early_leave': 'Về sớm',
      'sick_leave': 'Nghỉ ốm',
      'annual_leave': 'Nghỉ phép',
      'business_trip': 'Công tác'
    };
    return statusMap[status] || status;
  }

  /**
   * Tính toán summary cho báo cáo chấm công chi tiết
   */
  private calculateAttendanceSummary(attendances: any[]): any {
    const totalRecords = attendances.length;
    const presentRecords = attendances.filter(att => att.status === 'Có mặt').length;
    const lateRecords = attendances.filter(att => att.isLate).length;
    const earlyLeaveRecords = attendances.filter(att => att.isEarlyLeave).length;
    
    const totalWorkHours = attendances.reduce((sum, att) => sum + parseFloat(att.workHours || 0), 0);
    const totalOvertimeHours = attendances.reduce((sum, att) => sum + parseFloat(att.overtimeHours || 0), 0);

    return {
      totalItems: totalRecords,
      calculations: {
        totalRecords,
        presentRecords,
        absentRecords: totalRecords - presentRecords,
        lateRecords,
        earlyLeaveRecords,
        totalWorkHours: totalWorkHours.toFixed(2),
        totalOvertimeHours: totalOvertimeHours.toFixed(2),
        averageWorkHours: totalRecords > 0 ? (totalWorkHours / totalRecords).toFixed(2) : 0,
        attendanceRate: totalRecords > 0 ? ((presentRecords / totalRecords) * 100).toFixed(1) : 0,
        lateRate: totalRecords > 0 ? ((lateRecords / totalRecords) * 100).toFixed(1) : 0
      }
    };
  }

  /**
   * Group attendance by employee
   */
  private groupAttendanceByEmployee(attendances: any[], employeeMap: Map<number, any>): any[] {
    const grouped = attendances.reduce((acc, att) => {
      const employeeId = att.employeeId;
      if (!acc[employeeId]) {
        const employee = employeeMap.get(employeeId);
        acc[employeeId] = {
          employeeId,
          employeeCode: employee?.employeeCode || 'N/A',
          employeeName: employee?.employeeName || 'N/A',
          records: []
        };
      }
      acc[employeeId].records.push(att);
      return acc;
    }, {});

    return Object.values(grouped).map((group: any) => {
      const records = group.records;
      const totalWorkDays = records.length;
      const presentDays = records.filter(r => r.status === 'present').length;
      const lateDays = records.filter(r => this.isLateCheckIn(r.checkInTime)).length;
      const earlyLeaveDays = records.filter(r => this.isEarlyCheckOut(r.checkOutTime)).length;
      const absentDays = records.filter(r => r.status === 'absent').length;
      
      const totalWorkHours = records.reduce((sum, r) => sum + (r.workHours || 0) / 60, 0);
      const totalOvertimeHours = records.reduce((sum, r) => sum + (r.overtimeHours || 0) / 60, 0);

      return {
        employeeCode: group.employeeCode,
        employeeName: group.employeeName,
        totalWorkDays,
        totalWorkHours,
        totalOvertimeHours,
        lateDays,
        earlyLeaveDays,
        absentDays,
        averageWorkHours: totalWorkDays > 0 ? totalWorkHours / totalWorkDays : 0,
        attendanceRate: totalWorkDays > 0 ? presentDays / totalWorkDays : 0
      };
    });
  }

  /**
   * Tính toán summary tổng cho báo cáo tổng hợp
   */
  private calculateOverallAttendanceSummary(employeeSummaries: any[]): any {
    const totalEmployees = employeeSummaries.length;
    const totalWorkDays = employeeSummaries.reduce((sum, emp) => sum + emp.totalWorkDays, 0);
    const totalWorkHours = employeeSummaries.reduce((sum, emp) => sum + emp.totalWorkHours, 0);
    const totalOvertimeHours = employeeSummaries.reduce((sum, emp) => sum + emp.totalOvertimeHours, 0);
    const totalLateDays = employeeSummaries.reduce((sum, emp) => sum + emp.lateDays, 0);

    return {
      totalItems: totalEmployees,
      calculations: {
        totalEmployees,
        totalWorkDays,
        totalWorkHours: totalWorkHours.toFixed(2),
        totalOvertimeHours: totalOvertimeHours.toFixed(2),
        averageWorkHours: totalWorkDays > 0 ? (totalWorkHours / totalWorkDays).toFixed(2) : 0,
        averageAttendanceRate: totalEmployees > 0 ? 
          (employeeSummaries.reduce((sum, emp) => sum + emp.attendanceRate, 0) / totalEmployees * 100).toFixed(1) : 0,
        totalLateDays,
        lateRate: totalWorkDays > 0 ? ((totalLateDays / totalWorkDays) * 100).toFixed(1) : 0
      }
    };
  }
}
