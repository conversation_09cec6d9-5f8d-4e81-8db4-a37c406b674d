# 🧪 Test Reports Module

## Test Cases cho Reports Module

### 1. **Test Chat Commands**

#### Test Reports Agent
```
<PERSON><PERSON><PERSON> báo cáo nhân viên
Xuất báo cáo chấm công tháng này
Báo cáo thống kê nhân sự có biểu đồ
Tạo file Excel danh sách nhân viên phòng IT
Xuất chấm công chi tiết từ 1/12 đến 31/12
```

#### Expected Response Format
```
✅ Báo cáo [loại] đã được tạo thành công!

📊 **Thông tin báo cáo:**
- Tên file: [filename].xlsx
- Kích thước: [size] KB
- Số lượng: [count] records
- Thời gian tạo: [timestamp]

🔗 **Link download:** [S3 URL]

⏰ Link sẽ hết hạn sau 24 giờ.

📈 **Thống kê tóm tắt:**
- [Statistics summary]
```

### 2. **Test REST API Endpoints**

#### Test HR Employees Report
```bash
POST /api/v1/reports/hr/employees
Content-Type: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "filters": {
    "departmentIds": [1, 2],
    "status": ["active"]
  },
  "options": {
    "includeCharts": true,
    "includeStatistics": true,
    "format": "xlsx"
  },
  "includeSalary": false,
  "includePersonalInfo": true
}
```

#### Expected Response
```json
{
  "success": true,
  "data": {
    "fileName": "Bao_Cao_Nhan_Su_20241201_143022.xlsx",
    "downloadUrl": "https://s3.amazonaws.com/...",
    "fileSize": 1024000,
    "generatedAt": "2024-12-01T14:30:22.000Z",
    "expiresAt": "2024-12-02T14:30:22.000Z"
  },
  "statistics": {
    "totalEmployees": 150,
    "activeEmployees": 145,
    "averageAge": 28.5
  },
  "message": "Báo cáo danh sách nhân viên được tạo thành công"
}
```

### 3. **Test Agent Routing**

#### Test Intent Analysis
```javascript
// Test messages that should route to reports-agent
const testMessages = [
  "Tạo báo cáo nhân viên",
  "Xuất Excel chấm công",
  "Tải về file báo cáo",
  "Export employee list",
  "Báo cáo thống kê có biểu đồ"
];

// Expected routing
testMessages.forEach(async (message) => {
  const routing = await multiAgentService.testRouting(message);
  console.log(`"${message}" -> ${routing.selectedAgent}`);
  // Should route to 'reports-agent'
});
```

### 4. **Test Excel Generation**

#### Test Schema-based Generation
```typescript
const testSchema = {
  metadata: {
    title: "Test Report",
    author: "RedAI System"
  },
  sheets: [{
    name: "Test Data",
    columns: [
      { key: "id", header: "ID", type: "number", width: 10 },
      { key: "name", header: "Name", type: "string", width: 25 },
      { key: "date", header: "Date", type: "date", width: 15 }
    ],
    styling: {
      header: {
        font: { bold: true, color: "FFFFFF" },
        fill: { type: "pattern", fgColor: "4472C4" }
      },
      alternatingRows: true
    }
  }]
};

const testData = [
  { id: 1, name: "Test User 1", date: new Date() },
  { id: 2, name: "Test User 2", date: new Date() }
];

const result = await excelGeneratorService.generateFromSchema(
  testData, 
  testSchema, 
  { includeCharts: true, format: 'xlsx' }
);
```

### 5. **Test Error Handling**

#### Test Invalid Requests
```bash
# Missing required fields
POST /api/v1/reports/hr/employees
{}

# Invalid date range
POST /api/v1/reports/hr/employees
{
  "dateRange": {
    "startDate": "invalid-date",
    "endDate": "2024-12-31"
  }
}

# Invalid tenant access
# Should only return data for current tenant
```

### 6. **Test Performance**

#### Large Dataset Test
```typescript
// Test with large employee dataset (1000+ records)
const largeDataset = generateMockEmployees(1000);
const startTime = Date.now();

const result = await hrReportsService.getEmployeesReportData(
  tenantId,
  {},
  { startDate: "2024-01-01", endDate: "2024-12-31" }
);

const executionTime = Date.now() - startTime;
console.log(`Generated report for ${result.items.length} employees in ${executionTime}ms`);
```

### 7. **Test Security**

#### Tenant Isolation Test
```typescript
// Ensure reports only include data from current tenant
const tenant1Reports = await hrReportsService.getEmployeesReportData(1, {}, dateRange);
const tenant2Reports = await hrReportsService.getEmployeesReportData(2, {}, dateRange);

// Should have no overlapping employee IDs
const tenant1Ids = tenant1Reports.items.map(emp => emp.id);
const tenant2Ids = tenant2Reports.items.map(emp => emp.id);
const overlap = tenant1Ids.filter(id => tenant2Ids.includes(id));

expect(overlap.length).toBe(0);
```

### 8. **Test File Management**

#### S3 Integration Test
```typescript
// Test file upload and download
const buffer = await excelGeneratorService.generateFromSchema(data, schema);
const uploadResult = await s3Service.uploadBuffer(buffer, "test-report.xlsx");
const downloadUrl = await s3Service.getSignedUrl(uploadResult.key, 3600);

// Test URL accessibility
const response = await fetch(downloadUrl);
expect(response.status).toBe(200);
expect(response.headers.get('content-type')).toContain('spreadsheet');
```

### 9. **Test Multi-Agent Integration**

#### Test Agent Registration
```typescript
// Verify reports agent is registered
const agents = multiAgentService.getAgentCapabilities();
const reportsAgent = agents.find(agent => agent.name === 'reports-agent');

expect(reportsAgent).toBeDefined();
expect(reportsAgent.toolsCount).toBeGreaterThan(0);
expect(reportsAgent.capabilities).toContain('Tạo báo cáo Excel');
```

### 10. **Manual Testing Checklist**

- [ ] Chat commands work correctly
- [ ] Excel files download successfully
- [ ] Files open properly in Excel/LibreOffice
- [ ] Charts and styling display correctly
- [ ] Download links expire after 24h
- [ ] Only tenant data is included
- [ ] Error messages are helpful
- [ ] Performance is acceptable (<5s for 1000 records)
- [ ] Mobile browser compatibility
- [ ] Different date ranges work
- [ ] Filters work correctly
- [ ] Statistics are accurate

### 11. **Load Testing**

```bash
# Test concurrent report generation
for i in {1..10}; do
  curl -X POST "http://localhost:3000/api/v1/reports/hr/employees" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"dateRange":{"startDate":"2024-01-01","endDate":"2024-12-31"},"options":{"format":"xlsx"}}' &
done
wait
```

### 12. **Monitoring & Logging**

- Check application logs for errors
- Monitor S3 storage usage
- Track report generation times
- Monitor memory usage during large reports
- Check for memory leaks

---

**Run these tests after implementing the Reports Module to ensure everything works correctly!** 🚀
