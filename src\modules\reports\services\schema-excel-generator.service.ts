import { Injectable, Logger } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import * as moment from 'moment';
import {
  ExcelReportSchema,
  ExcelSheetSchema,
  ExcelColumnSchema,
  ExcelChartSchema,
  ExcelGenerationResult,
  ExcelGenerationOptions,
  ExcelCellStyle,
  ExcelColumnType,
  ExcelChartType
} from '../interfaces';

/**
 * Service core để generate Excel từ JSON schema
 * Hỗ trợ đầy đủ tính năng styling, charts, formulas
 */
@Injectable()
export class SchemaExcelGeneratorService {
  private readonly logger = new Logger(SchemaExcelGeneratorService.name);

  /**
   * Generate Excel file từ schema và data
   */
  async generateFromSchema(
    data: any[],
    schema: ExcelReportSchema,
    options: ExcelGenerationOptions = {
      includeCharts: true,
      includeStatistics: true,
      format: 'xlsx'
    }
  ): Promise<ExcelGenerationResult> {
    try {
      this.logger.log(`<PERSON>ắt đầu generate Excel: ${schema.metadata.title}`);
      
      const workbook = new ExcelJS.Workbook();
      
      // Set metadata cho workbook
      this.setWorkbookMetadata(workbook, schema);
      
      // Tạo các sheets
      for (const sheetSchema of schema.sheets) {
        await this.createSheet(workbook, data, sheetSchema, options);
      }
      
      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer() as Buffer;

      const result: ExcelGenerationResult = {
        success: true,
        fileName: this.generateFileName(schema.metadata.title),
        fileSize: buffer.byteLength,
        buffer: buffer,
        generatedAt: new Date(),
        metadata: schema.metadata
      };
      
      this.logger.log(`Hoàn thành generate Excel: ${result.fileName}`);
      return result;
      
    } catch (error) {
      this.logger.error(`Lỗi generate Excel: ${error.message}`, error.stack);
      throw new Error(`Không thể tạo file Excel: ${error.message}`);
    }
  }

  /**
   * Set metadata cho workbook
   */
  private setWorkbookMetadata(workbook: ExcelJS.Workbook, schema: ExcelReportSchema): void {
    workbook.creator = schema.metadata.author || 'RedAI System';
    workbook.title = schema.metadata.title;
    workbook.description = schema.metadata.description || '';
    workbook.company = schema.metadata.company || 'RedAI';
    workbook.keywords = schema.metadata.keywords?.join(', ') || '';
    workbook.created = new Date();
    workbook.modified = new Date();
  }

  /**
   * Tạo một sheet từ schema
   */
  private async createSheet(
    workbook: ExcelJS.Workbook,
    data: any[],
    schema: ExcelSheetSchema,
    options: ExcelGenerationOptions
  ): Promise<void> {
    const worksheet = workbook.addWorksheet(schema.name);
    
    // Set columns từ schema
    this.setupColumns(worksheet, schema.columns);
    
    // Apply header styling
    this.applyHeaderStyling(worksheet, schema);
    
    // Add data rows
    this.addDataRows(worksheet, data, schema);
    
    // Apply data styling
    this.applyDataStyling(worksheet, schema, data.length);
    
    // Add summary nếu có
    if (schema.summary) {
      this.addSummarySection(worksheet, schema, data);
    }
    
    // Add charts nếu được yêu cầu
    if (options.includeCharts && schema.charts) {
      for (const chartSchema of schema.charts) {
        await this.addChart(worksheet, chartSchema, data.length);
      }
    }
    
    // Apply filters
    if (schema.styling?.autoFilter) {
      this.applyAutoFilter(worksheet, schema.columns.length);
    }
    
    // Freeze header nếu được yêu cầu
    if (schema.styling?.freezeHeader) {
      worksheet.views = [{ state: 'frozen', ySplit: 1 }];
    }
  }

  /**
   * Setup columns cho worksheet
   */
  private setupColumns(worksheet: ExcelJS.Worksheet, columns: ExcelColumnSchema[]): void {
    worksheet.columns = columns.map(col => ({
      key: col.key,
      header: col.header,
      width: col.width || this.getDefaultColumnWidth(col.type),
      hidden: col.hidden || false
    }));
  }

  /**
   * Apply styling cho header
   */
  private applyHeaderStyling(worksheet: ExcelJS.Worksheet, schema: ExcelSheetSchema): void {
    if (!schema.styling?.header) return;
    
    const headerRow = worksheet.getRow(1);
    const style = schema.styling.header;
    
    // Apply font styling
    if (style.font) {
      headerRow.font = {
        bold: style.font.bold || false,
        italic: style.font.italic || false,
        underline: style.font.underline || false,
        color: style.font.color ? { argb: style.font.color } : undefined,
        size: style.font.size || 12,
        name: style.font.name || 'Arial'
      };
    }
    
    // Apply fill styling
    if (style.fill) {
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: style.fill.fgColor || 'FF4472C4' }
      };
    }
    
    // Apply border styling
    if (style.border) {
      headerRow.border = this.convertBorderStyle(style.border);
    }
    
    // Apply alignment
    if (style.alignment) {
      headerRow.alignment = style.alignment;
    }
    
    headerRow.commit();
  }

  /**
   * Add data rows vào worksheet
   */
  private addDataRows(worksheet: ExcelJS.Worksheet, data: any[], schema: ExcelSheetSchema): void {
    data.forEach((item, index) => {
      const rowData: any = {};
      
      schema.columns.forEach(col => {
        let value = item[col.key];
        
        // Format value theo type
        value = this.formatValueByType(value, col.type, col.format);
        
        // Apply formula nếu có
        if (col.formula) {
          value = { formula: col.formula.replace(/\{row\}/g, (index + 2).toString()) };
        }
        
        rowData[col.key] = value;
      });
      
      worksheet.addRow(rowData);
    });
  }

  /**
   * Apply styling cho data rows
   */
  private applyDataStyling(worksheet: ExcelJS.Worksheet, schema: ExcelSheetSchema, dataLength: number): void {
    if (!schema.styling) return;
    
    const startRow = 2; // Bắt đầu từ row 2 (sau header)
    const endRow = startRow + dataLength - 1;
    
    for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
      const row = worksheet.getRow(rowIndex);
      
      // Apply data styling
      if (schema.styling.data) {
        this.applyCellStyle(row, schema.styling.data);
      }
      
      // Apply alternating row colors
      if (schema.styling.alternatingRows && (rowIndex - startRow) % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: schema.styling.alternatingRowColor || 'FFF2F2F2' }
        };
      }
      
      row.commit();
    }
  }

  /**
   * Format value theo type
   */
  private formatValueByType(value: any, type: ExcelColumnType, format?: string): any {
    if (value === null || value === undefined) return '';
    
    switch (type) {
      case 'date':
        return moment(value).isValid() ? moment(value).toDate() : value;
      case 'datetime':
        return moment(value).isValid() ? moment(value).toDate() : value;
      case 'currency':
        return typeof value === 'number' ? value : parseFloat(value) || 0;
      case 'percentage':
        return typeof value === 'number' ? value / 100 : parseFloat(value) / 100 || 0;
      case 'number':
        return typeof value === 'number' ? value : parseFloat(value) || 0;
      case 'boolean':
        return Boolean(value);
      default:
        return value;
    }
  }

  /**
   * Get default column width theo type
   */
  private getDefaultColumnWidth(type: ExcelColumnType): number {
    switch (type) {
      case 'date':
      case 'datetime':
        return 15;
      case 'currency':
        return 12;
      case 'number':
        return 10;
      case 'boolean':
        return 8;
      case 'email':
        return 25;
      case 'phone':
        return 15;
      case 'url':
        return 30;
      default:
        return 20;
    }
  }

  /**
   * Generate file name
   */
  private generateFileName(title: string): string {
    const timestamp = moment().format('YYYYMMDD_HHmmss');
    const cleanTitle = title.replace(/[^a-zA-Z0-9\u00C0-\u024F\u1E00-\u1EFF]/g, '_');
    return `${cleanTitle}_${timestamp}.xlsx`;
  }

  /**
   * Convert border style
   */
  private convertBorderStyle(border: any): any {
    // Implementation for border conversion
    return border;
  }

  /**
   * Apply cell style
   */
  private applyCellStyle(row: ExcelJS.Row, style: ExcelCellStyle): void {
    // Implementation for applying cell style
  }

  /**
   * Add summary section
   */
  private addSummarySection(worksheet: ExcelJS.Worksheet, schema: ExcelSheetSchema, data: any[]): void {
    // Implementation for summary section
  }

  /**
   * Add chart
   */
  private async addChart(worksheet: ExcelJS.Worksheet, chartSchema: ExcelChartSchema, dataLength: number): Promise<void> {
    // Implementation for adding charts
  }

  /**
   * Apply auto filter
   */
  private applyAutoFilter(worksheet: ExcelJS.Worksheet, columnCount: number): void {
    worksheet.autoFilter = {
      from: 'A1',
      to: String.fromCharCode(64 + columnCount) + '1'
    };
  }
}
