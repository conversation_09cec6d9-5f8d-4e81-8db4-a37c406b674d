import {
  Controller,
  Get,
  Param,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { RoleService } from '../services/role.service';
import { EmployeeService } from '../services/employee.service';
import { UserRolesResponseDto } from '../dto/role/user-roles.dto';
// import { UserRepository } from '@/modules/auth/repositories/user.repository'; // REMOVED: UserRepository đã được xóa
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

/**
 * Controller quản lý vai trò của nhân viên
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiExtraModels(ApiResponseDto, UserRolesResponseDto)
@Controller('api/hrm/employees')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeRoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly employeeService: EmployeeService,
    // private readonly userRepository: UserRepository, // REMOVED: UserRepository đã được xóa
  ) {}

  /**
   * Lấy danh sách vai trò của nhân viên theo ID
   */
  @Get(':employeeId/roles')
  @RequirePermissionEnum(Permission.USER_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy danh sách vai trò của nhân viên theo ID' })
  @ApiParam({
    name: 'employeeId',
    description: 'ID của nhân viên',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách vai trò của nhân viên',
    schema: ApiResponseDto.getSchema(UserRolesResponseDto),
  })
  async getEmployeeRoles(
    @CurrentUser() user: JwtPayload,
    @Param('employeeId', ParseIntPipe) employeeId: number,
  ): Promise<ApiResponseDto<UserRolesResponseDto>> {
    // Lấy thông tin nhân viên
    const employee = await this.employeeService.findById(
      Number(user.tenantId),
      employeeId,
    );

    // Tìm user tương ứng với employee này
    // REMOVED: UserRepository đã được xóa, sử dụng employee.id trực tiếp
    // const userForEmployee = await this.userRepository.findByEmployeeId(
    //   employee.id,
    // );
    // if (!userForEmployee) {
    //   throw new AppException(
    //     HRM_ERROR_CODES.USER_NOT_FOUND,
    //     `Không tìm thấy tài khoản người dùng cho nhân viên ID ${employeeId}`,
    //   );
    // }

    // Sử dụng employee.id trực tiếp vì User.id = Employee.id
    const userForEmployee = { id: employee.id };

    // Lấy danh sách vai trò của người dùng
    const result = await this.roleService.getUserRoles(userForEmployee.id);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách vai trò của nhân viên hiện tại
   */
  @Get('me/roles')
  @ApiOperation({ summary: 'Lấy danh sách vai trò của nhân viên hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách vai trò của nhân viên hiện tại',
    schema: ApiResponseDto.getSchema(UserRolesResponseDto),
  })
  async getCurrentEmployeeRoles(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserRolesResponseDto>> {
    // Lấy danh sách vai trò của người dùng hiện tại (user.id chính là userId)
    const result = await this.roleService.getUserRoles(user.id);
    return ApiResponseDto.success(result);
  }
}
