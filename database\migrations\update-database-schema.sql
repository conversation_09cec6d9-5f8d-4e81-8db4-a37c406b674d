-- =====================================================
-- SCHEMA UPDATE: Cập nhật database.sql với cấu trúc mới
-- Thay thế cấu trúc employees cũ bằng cấu trúc mới
-- Ngày: 2025-06-18
-- =====================================================

-- =====================================================
-- EMPLOYEES TABLE - CẤU TRÚC MỚI
-- =====================================================

DROP TABLE IF EXISTS employees CASCADE;

CREATE TABLE employees (
    id SERIAL CONSTRAINT employees_pk PRIMARY KEY,
    
    -- Authentication fields (thay thế Users)
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255), -- Có thể null ban đầu
    account_status VARCHAR(20) DEFAULT 'PENDING' NOT NULL,
    tenant_id INTEGER NOT NULL,
    
    -- HR fields
    employee_code VARCHAR(50) NOT NULL UNIQUE,
    employee_name VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    department_id INTEGER,
    manager_id INTEGER,
    hire_date DATE,
    employment_type VARCHAR(50) DEFAULT 'FULL_TIME' NOT NULL,
    salary DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    
    -- Personal information
    date_of_birth DATE,
    gender VARCHAR(10),
    marital_status VARCHAR(20),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    
    -- Financial information
    bank_account_number VARCHAR(50),
    bank_name VARCHAR(255),
    social_insurance_number VARCHAR(50),
    tax_id VARCHAR(50),
    
    -- Additional fields
    notes TEXT,
    
    -- Timestamps
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    
    -- Constraints
    CONSTRAINT employees_account_status_check 
        CHECK (account_status IN ('ACTIVE', 'INACTIVE', 'PENDING')),
    CONSTRAINT employees_employment_type_check 
        CHECK (employment_type IN ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN')),
    CONSTRAINT employees_status_check 
        CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave')),
    CONSTRAINT employees_gender_check 
        CHECK (gender IN ('male', 'female', 'other')),
    CONSTRAINT employees_marital_status_check 
        CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed'))
);

-- Comments cho bảng employees
COMMENT ON TABLE employees IS 'Bảng nhân viên - xử lý cả authentication và HR functions';

-- Comments cho các cột
COMMENT ON COLUMN employees.id IS 'Định danh duy nhất cho nhân viên (primary key cho cả authentication và HR)';
COMMENT ON COLUMN employees.email IS 'Email nhân viên (dùng cho authentication)';
COMMENT ON COLUMN employees.password IS 'Mật khẩu đã mã hóa (dùng cho authentication)';
COMMENT ON COLUMN employees.account_status IS 'Trạng thái tài khoản authentication (ACTIVE, INACTIVE, PENDING)';
COMMENT ON COLUMN employees.tenant_id IS 'ID tenant cho multi-tenant isolation (required)';
COMMENT ON COLUMN employees.employee_code IS 'Mã nhân viên duy nhất (REDAI + số)';
COMMENT ON COLUMN employees.employee_name IS 'Tên đầy đủ của nhân viên';
COMMENT ON COLUMN employees.position IS 'Chức vụ/vị trí công việc';
COMMENT ON COLUMN employees.department_id IS 'ID phòng ban';
COMMENT ON COLUMN employees.manager_id IS 'ID người quản lý trực tiếp';
COMMENT ON COLUMN employees.hire_date IS 'Ngày bắt đầu làm việc';
COMMENT ON COLUMN employees.employment_type IS 'Loại hợp đồng (FULL_TIME, PART_TIME, CONTRACT, INTERN)';
COMMENT ON COLUMN employees.salary IS 'Mức lương cơ bản';
COMMENT ON COLUMN employees.status IS 'Trạng thái HR (active, inactive, terminated, on_leave)';
COMMENT ON COLUMN employees.date_of_birth IS 'Ngày sinh';
COMMENT ON COLUMN employees.gender IS 'Giới tính (male, female, other)';
COMMENT ON COLUMN employees.marital_status IS 'Tình trạng hôn nhân';
COMMENT ON COLUMN employees.emergency_contact_name IS 'Tên người liên hệ khẩn cấp';
COMMENT ON COLUMN employees.emergency_contact_phone IS 'SĐT người liên hệ khẩn cấp';
COMMENT ON COLUMN employees.bank_account_number IS 'Số tài khoản ngân hàng';
COMMENT ON COLUMN employees.bank_name IS 'Tên ngân hàng';
COMMENT ON COLUMN employees.social_insurance_number IS 'Số bảo hiểm xã hội';
COMMENT ON COLUMN employees.tax_id IS 'Mã số thuế cá nhân';
COMMENT ON COLUMN employees.notes IS 'Ghi chú thêm';
COMMENT ON COLUMN employees.created_at IS 'Thời gian tạo (Unix timestamp)';
COMMENT ON COLUMN employees.updated_at IS 'Thời gian cập nhật (Unix timestamp)';

-- Indexes cho performance
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_employees_tenant_email ON employees(tenant_id, email);
CREATE INDEX idx_employees_account_status ON employees(account_status);
CREATE INDEX idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_manager_id ON employees(manager_id);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_employees_employee_code ON employees(employee_code);
CREATE INDEX idx_employees_employee_name ON employees(employee_name);

-- Owner
ALTER TABLE employees OWNER TO root;

-- =====================================================
-- CẬP NHẬT CÁC BẢNG LIÊN QUAN
-- =====================================================

-- Cập nhật employee_has_roles để reference employees mới
ALTER TABLE employee_has_roles 
DROP CONSTRAINT IF EXISTS employee_has_roles_employees_id_fk;

ALTER TABLE employee_has_roles 
ADD CONSTRAINT employee_has_roles_employees_id_fk 
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

-- Cập nhật admin_tags references
ALTER TABLE admin_tags 
DROP CONSTRAINT IF EXISTS admin_tags_employees_id_fk;

ALTER TABLE admin_tags 
DROP CONSTRAINT IF EXISTS admin_tags_employees_id_fk_2;

ALTER TABLE admin_tags 
ADD CONSTRAINT admin_tags_employees_created_by_fk 
FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL;

ALTER TABLE admin_tags 
ADD CONSTRAINT admin_tags_employees_updated_by_fk 
FOREIGN KEY (updated_by) REFERENCES employees(id) ON DELETE SET NULL;

-- =====================================================
-- TẠO VIEW USERS ĐỂ TƯƠNG THÍCH NGƯỢC
-- =====================================================

-- Tạo view users để tương thích với code cũ
CREATE OR REPLACE VIEW users AS
SELECT 
    id,
    employee_name as full_name,
    email,
    NULL as phone_number,
    CASE 
        WHEN account_status = 'ACTIVE' THEN true
        ELSE false
    END as is_active,
    false as is_verify_email,
    created_at,
    updated_at,
    NULL as citizen_issue_place,
    NULL as citizen_issue_date,
    false as is_first_password_change,
    NULL as country,
    NULL as address,
    tax_id as tax_code,
    0 as points_balance,
    'INDIVIDUAL' as type,
    NULL as platform,
    NULL as citizen_id,
    NULL as avatar,
    password,
    date_of_birth,
    gender,
    NULL as bank_code,
    bank_account_number as account_number,
    NULL as account_holder,
    NULL as bank_branch,
    'employee' as role,
    false as is_verify_phone,
    NULL as vector_store_key,
    NULL as alert_threshold,
    false as was_rpoint_alerted,
    tenant_id
FROM employees;

COMMENT ON VIEW users IS 'View tương thích ngược - map employees data sang users format';

-- =====================================================
-- CẬP NHẬT FOREIGN KEY REFERENCES
-- =====================================================

-- Cập nhật các bảng reference user_id để reference employees
-- (Giữ nguyên tên cột user_id để tương thích code)

-- user_roles
ALTER TABLE user_roles 
DROP CONSTRAINT IF EXISTS user_roles_users_id_fk;

ALTER TABLE user_roles 
ADD CONSTRAINT user_roles_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- business_info
ALTER TABLE business_info 
DROP CONSTRAINT IF EXISTS business_info_users_id_fk;

ALTER TABLE business_info 
ADD CONSTRAINT business_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- two_factor_auth
ALTER TABLE two_factor_auth 
DROP CONSTRAINT IF EXISTS two_factor_auth_users_id_fk;

ALTER TABLE two_factor_auth 
ADD CONSTRAINT two_factor_auth_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- device_info
ALTER TABLE device_info 
DROP CONSTRAINT IF EXISTS device_info_users_id_fk;

ALTER TABLE device_info 
ADD CONSTRAINT device_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- auth_verification_logs
ALTER TABLE auth_verification_logs 
DROP CONSTRAINT IF EXISTS auth_verification_logs_users_id_fk;

ALTER TABLE auth_verification_logs 
ADD CONSTRAINT auth_verification_logs_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- point_purchase_transactions
ALTER TABLE point_purchase_transactions 
DROP CONSTRAINT IF EXISTS point_purchase_transactions_users_id_fk;

ALTER TABLE point_purchase_transactions 
ADD CONSTRAINT point_purchase_transactions_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- affiliate_accounts
ALTER TABLE affiliate_accounts 
DROP CONSTRAINT IF EXISTS affiliate_accounts_users_id_fk;

ALTER TABLE affiliate_accounts 
ADD CONSTRAINT affiliate_accounts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- affiliate_contracts
ALTER TABLE affiliate_contracts 
DROP CONSTRAINT IF EXISTS affiliate_contracts_users_id_fk;

ALTER TABLE affiliate_contracts 
ADD CONSTRAINT affiliate_contracts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Thêm sample employee data
INSERT INTO employees (
    email, password, account_status, tenant_id, employee_code, employee_name,
    employment_type, status, hire_date, created_at, updated_at
) VALUES 
(
    '<EMAIL>', 
    '$2b$10$example.hash.password', 
    'ACTIVE', 
    1, 
    'REDAI001', 
    'System Administrator',
    'FULL_TIME', 
    'active', 
    CURRENT_DATE, 
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Kiểm tra cấu trúc bảng
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'employees' 
ORDER BY ordinal_position;

-- Kiểm tra constraints
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'employees';

-- Kiểm tra indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'employees';

SELECT 'EMPLOYEES TABLE STRUCTURE UPDATED SUCCESSFULLY' as status;
