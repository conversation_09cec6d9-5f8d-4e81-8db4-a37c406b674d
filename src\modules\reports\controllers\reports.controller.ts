import { Controller, Post, Body, UseGuards, Logger, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { SchemaExcelGeneratorService } from '../services/schema-excel-generator.service';
import { ReportTemplateService } from '../services/report-template.service';
import { HRReportsService } from '../services/hr-reports.service';
import { AttendanceReportsService } from '../services/attendance-reports.service';
import { S3Service } from '@shared/services/s3.service';
import { 
  BaseReportRequestDto, 
  BaseReportResponseDto,
  HREmployeesReportRequestDto,
  HREmployeesReportResponseDto 
} from '../dto';
import { ReportType } from '../enums';
import { ExcelGenerationOptions } from '../interfaces';

/**
 * Controller chính cho hệ thống báo cáo
 */
@ApiTags('Reports')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('reports')
export class ReportsController {
  private readonly logger = new Logger(ReportsController.name);

  constructor(
    private readonly excelGeneratorService: SchemaExcelGeneratorService,
    private readonly templateService: ReportTemplateService,
    private readonly hrReportsService: HRReportsService,
    private readonly attendanceReportsService: AttendanceReportsService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Lấy danh sách các loại báo cáo có sẵn
   */
  @Get('types')
  @ApiOperation({ summary: 'Lấy danh sách loại báo cáo' })
  @ApiResponse({ 
    status: 200, 
    description: 'Danh sách loại báo cáo thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'hr_employees' },
              name: { type: 'string', example: 'Báo cáo danh sách nhân viên' },
              description: { type: 'string', example: 'Danh sách chi tiết thông tin nhân viên' },
              category: { type: 'string', example: 'HR' }
            }
          }
        }
      }
    }
  })
  async getReportTypes() {
    try {
      const reportTypes = [
        {
          type: ReportType.HR_EMPLOYEES,
          name: 'Báo cáo danh sách nhân viên',
          description: 'Danh sách chi tiết thông tin nhân viên trong công ty',
          category: 'HR'
        },
        {
          type: ReportType.HR_STATISTICS,
          name: 'Báo cáo thống kê nhân sự',
          description: 'Thống kê tổng quan về nhân sự công ty',
          category: 'HR'
        },
        {
          type: ReportType.ATTENDANCE_DETAILS,
          name: 'Báo cáo chấm công chi tiết',
          description: 'Chi tiết chấm công của nhân viên theo thời gian',
          category: 'Attendance'
        },
        {
          type: ReportType.ATTENDANCE_SUMMARY,
          name: 'Báo cáo tổng hợp chấm công',
          description: 'Tổng hợp chấm công theo nhân viên',
          category: 'Attendance'
        },
        {
          type: ReportType.OKR_PROGRESS,
          name: 'Báo cáo tiến độ OKR',
          description: 'Tiến độ thực hiện các mục tiêu OKR',
          category: 'OKR'
        },
        {
          type: ReportType.PROJECT_PROGRESS,
          name: 'Báo cáo tiến độ dự án',
          description: 'Tiến độ thực hiện các dự án',
          category: 'Project'
        }
      ];

      return {
        success: true,
        data: reportTypes
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách loại báo cáo: ${error.message}`, error.stack);
      return {
        success: false,
        error: 'Không thể lấy danh sách loại báo cáo'
      };
    }
  }

  /**
   * Tạo báo cáo tùy chỉnh từ request
   */
  @Post('generate')
  @ApiOperation({ summary: 'Tạo báo cáo tùy chỉnh' })
  @ApiResponse({ 
    status: 200, 
    description: 'Tạo báo cáo thành công',
    type: BaseReportResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Lỗi server khi tạo báo cáo' 
  })
  async generateReport(
    @Body() request: BaseReportRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<BaseReportResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo báo cáo ${request.reportType} cho user ${user.id}`);

      // Lấy template schema theo loại báo cáo
      const schema = this.templateService.getTemplateByType(request.reportType);

      // Lấy dữ liệu theo loại báo cáo
      const reportData = await this.getReportData(
        request.reportType,
        user.tenantId as number,
        request.filters || {},
        request.dateRange
      );

      // Tạo options cho Excel generation
      const options: ExcelGenerationOptions = {
        includeCharts: request.options.includeCharts ?? true,
        includeStatistics: request.options.includeStatistics ?? true,
        format: request.options.format
      };

      // Generate Excel file
      const result = await this.excelGeneratorService.generateFromSchema(
        reportData.items,
        schema,
        options
      );

      // Upload file lên S3
      const s3Key = `reports/${result.fileName}`;
      await this.s3Service.uploadBuffer(
        s3Key,
        result.buffer!,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
      );

      // Tạo temporary download URL (24 hours)
      const downloadUrl = await this.s3Service.getDownloadUrl(s3Key);

      const response: BaseReportResponseDto = {
        success: true,
        data: {
          fileName: result.fileName,
          downloadUrl,
          fileSize: result.fileSize,
          generatedAt: result.generatedAt.toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        message: 'Báo cáo được tạo thành công'
      };

      this.logger.log(`Hoàn thành tạo báo cáo: ${result.fileName}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi tạo báo cáo: ${error.message}`, error.stack);
      return {
        success: false,
        data: null as any,
        error: `Không thể tạo báo cáo: ${error.message}`
      };
    }
  }

  /**
   * Lấy dữ liệu báo cáo theo loại
   */
  private async getReportData(
    reportType: ReportType,
    tenantId: number,
    filters: any,
    dateRange: any
  ): Promise<any> {
    switch (reportType) {
      case ReportType.HR_EMPLOYEES:
        return await this.hrReportsService.getEmployeesReportData(tenantId, filters, dateRange);
      
      case ReportType.HR_STATISTICS:
        return await this.hrReportsService.getHRStatisticsReportData(tenantId, filters, dateRange);
      
      case ReportType.ATTENDANCE_DETAILS:
        return await this.attendanceReportsService.getAttendanceDetailsReportData(tenantId, filters, dateRange);
      
      case ReportType.ATTENDANCE_SUMMARY:
        return await this.attendanceReportsService.getAttendanceSummaryReportData(tenantId, filters, dateRange);
      
      // TODO: Implement OKR và Project reports
      case ReportType.OKR_PROGRESS:
      case ReportType.PROJECT_PROGRESS:
        throw new Error(`Báo cáo ${reportType} chưa được triển khai`);
      
      default:
        throw new Error(`Loại báo cáo không được hỗ trợ: ${reportType}`);
    }
  }
}
