import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@/common';
import { EncryptionService } from '@/shared/services/encryption.service';
import { JwtUtilService, TokenType } from '@/modules/auth/guards/jwt.util';
import { PermissionRepository } from '@/modules/auth/repositories/permission.repository';
import { AUTH_ERROR_CODE } from '@/modules/auth/errors/auth-error.code';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { EmployeeRepository } from '../repositories/employee.repository';
import { Employee } from '../entities/employee.entity';
import { UserLoginDto } from '@/modules/auth/dto/user-login.dto';
import { UserLoginResponseDto } from '@/modules/auth/dto/user-response.dto';
import { UserResponseDto } from '@/modules/auth/dto/user-response.dto';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';
import { ChangePasswordDto, ChangePasswordResponseDto } from '@/modules/auth/dto/change-password.dto';
import { AdminChangePasswordDto, AdminChangePasswordResponseDto } from '@/modules/auth/dto/admin-change-password.dto';

/**
 * Service xử lý authentication cho Employee (thay thế UserAuthService)
 * Employee entity sẽ thay thế hoàn toàn User entity cho authentication
 */
@Injectable()
export class EmployeeAuthService {
  private readonly logger = new Logger(EmployeeAuthService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly encryptionService: EncryptionService,
    private readonly jwtService: JwtUtilService,
    private readonly permissionRepository: PermissionRepository,
  ) {}

  /**
   * Đăng nhập bằng Employee credentials
   * @param loginDto Thông tin đăng nhập
   * @returns Token và thông tin employee
   */
  async login(loginDto: UserLoginDto): Promise<ApiResponseDto<UserLoginResponseDto>> {
    console.log('🔐 [EMPLOYEE LOGIN] Bắt đầu đăng nhập với username:', loginDto.username);

    // Tìm employee theo email (username có thể là email)
    const employee = await this.employeeRepository.findByEmail(loginDto.username);
    if (!employee) {
      console.log('❌ [EMPLOYEE LOGIN] Không tìm thấy employee với username:', loginDto.username);
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CREDENTIALS,
        'Email hoặc mật khẩu không chính xác.',
      );
    }

    console.log('✅ [EMPLOYEE LOGIN] Tìm thấy employee:', {
      id: employee.id,
      email: employee.email,
      employeeName: employee.employeeName,
      accountStatus: employee.accountStatus,
      tenantId: employee.tenantId
    });

    // Kiểm tra mật khẩu
    if (!employee.password) {
      console.log('❌ [EMPLOYEE LOGIN] Employee chưa có mật khẩu:', employee.id);
      throw new AppException(
        AUTH_ERROR_CODE.ACCOUNT_NOT_VERIFIED,
        'Tài khoản chưa được thiết lập mật khẩu.',
      );
    }

    const isPasswordValid = this.encryptionService.verifyPassword(
      loginDto.password,
      employee.password,
    );

    if (!isPasswordValid) {
      console.log('❌ [EMPLOYEE LOGIN] Mật khẩu không chính xác cho employee:', employee.id);
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CREDENTIALS,
        'Email hoặc mật khẩu không chính xác.',
      );
    }

    console.log('✅ [EMPLOYEE LOGIN] Mật khẩu chính xác');

    // Kiểm tra trạng thái tài khoản
    if (!employee.canLogin()) {
      console.log('❌ [EMPLOYEE LOGIN] Employee không thể đăng nhập:', {
        id: employee.id,
        accountStatus: employee.accountStatus,
        status: employee.status,
        hasPassword: !!employee.password
      });
      throw new AppException(
        AUTH_ERROR_CODE.ACCOUNT_LOCKED,
        'Tài khoản chưa được kích hoạt hoặc đã bị khóa.',
      );
    }

    console.log('✅ [EMPLOYEE LOGIN] Trạng thái tài khoản hợp lệ');

    // Lấy danh sách quyền của employee
    console.log('🔐 [EMPLOYEE LOGIN] Lấy danh sách quyền cho employee:', employee.id);
    const permissions = await this.permissionRepository.getUserPermissions(employee.id);
    console.log('✅ [EMPLOYEE LOGIN] Lấy được quyền:', {
      employeeId: employee.id,
      permissionCount: permissions.length,
      permissions: permissions
    });

    // Tạo JWT token
    const payload = {
      id: employee.id,
      sub: employee.id,
      email: employee.email,
      name: employee.getDisplayName(),
      typeToken: TokenType.ACCESS,
      type: 'EMPLOYEE' as const,
      tenantId: employee.tenantId,
      permissions: permissions,
    };

    console.log('🎫 [EMPLOYEE LOGIN] Tạo JWT token với payload:', {
      employeeId: payload.id,
      email: payload.email,
      name: payload.name,
      tenantId: payload.tenantId,
      permissionCount: payload.permissions.length
    });

    const { token: accessToken } = this.jwtService.generateToken(
      payload,
      TokenType.ACCESS,
      '24h',
    );

    console.log('✅ [EMPLOYEE LOGIN] Tạo token thành công, độ dài token:', accessToken.length);

    // Tạo response DTO từ employee data
    const userResponseDto = this.mapEmployeeToUserResponse(employee);

    const response: UserLoginResponseDto = {
      accessToken,
      user: userResponseDto,
      permissions: permissions,
    };

    console.log('🎉 [EMPLOYEE LOGIN] Đăng nhập thành công:', {
      employeeId: employee.id,
      email: employee.email,
      employeeName: employee.employeeName,
      tenantId: employee.tenantId,
      permissionCount: permissions.length,
      timestamp: new Date().toISOString()
    });

    return ApiResponseDto.success(response);
  }

  /**
   * Lấy thông tin employee profile từ token
   * @param employeeId ID của employee
   * @returns Thông tin employee và danh sách quyền
   */
  async getEmployeeProfile(
    employeeId: number,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    const employee = await this.employeeRepository.findById(0, employeeId); // Tìm không cần tenantId vì đã có trong token

    if (!employee) {
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Không tìm thấy thông tin employee',
      );
    }

    // Lấy danh sách quyền của employee
    const permissions = await this.permissionRepository.getUserPermissions(employee.id);

    // Tạo response DTO
    const userResponseDto = this.mapEmployeeToUserResponse(employee);

    const response: UserLoginResponseDto = {
      accessToken: '', // Không cần token mới khi get profile
      user: userResponseDto,
      permissions: permissions,
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Đổi mật khẩu employee (compatible với UserAuthService interface)
   * @param employeeId ID employee
   * @param changePasswordDto DTO chứa thông tin đổi mật khẩu
   * @returns Success response
   */
  async changePassword(
    employeeId: number,
    changePasswordDto: ChangePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    // Lấy employee để có tenantId
    const employee = await this.employeeRepository.findById(0, employeeId); // Tìm không cần tenantId filter
    if (!employee) {
      throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND, 'Không tìm thấy thông tin employee');
    }

    // Kiểm tra mật khẩu mới và xác nhận mật khẩu
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new AppException(AUTH_ERROR_CODE.PASSWORD_MISMATCH, 'Mật khẩu mới và xác nhận mật khẩu không khớp');
    }

    // Kiểm tra mật khẩu cũ
    if (employee.password) {
      const isOldPasswordValid = this.encryptionService.verifyPassword(
        changePasswordDto.currentPassword,
        employee.password,
      );

      if (!isOldPasswordValid) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_CURRENT_PASSWORD, 'Mật khẩu hiện tại không chính xác');
      }
    }

    // Hash mật khẩu mới
    const hashedNewPassword = this.encryptionService.hashPassword(changePasswordDto.newPassword);

    // Cập nhật mật khẩu
    await this.employeeRepository.updatePassword(employee.tenantId, employeeId, hashedNewPassword);

    return ApiResponseDto.success({
      message: 'Đổi mật khẩu thành công',
    });
  }

  /**
   * Đổi mật khẩu employee (method gốc với parameters riêng)
   * @param employeeId ID employee
   * @param tenantId Tenant ID
   * @param oldPassword Mật khẩu cũ
   * @param newPassword Mật khẩu mới
   * @returns Success response
   */
  async changePasswordDirect(
    employeeId: number,
    tenantId: number,
    oldPassword: string,
    newPassword: string,
  ): Promise<ApiResponseDto<{ message: string }>> {
    const employee = await this.employeeRepository.findById(tenantId, employeeId);
    
    if (!employee) {
      throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND, 'Không tìm thấy thông tin employee');
    }

    // Kiểm tra mật khẩu cũ
    if (employee.password) {
      const isOldPasswordValid = this.encryptionService.verifyPassword(
        oldPassword,
        employee.password,
      );

      if (!isOldPasswordValid) {
        throw new AppException(AUTH_ERROR_CODE.INVALID_CURRENT_PASSWORD, 'Mật khẩu cũ không chính xác');
      }
    }

    // Hash mật khẩu mới
    const hashedNewPassword = this.encryptionService.hashPassword(newPassword);

    // Cập nhật mật khẩu
    await this.employeeRepository.updatePassword(tenantId, employeeId, hashedNewPassword);

    return ApiResponseDto.success({
      message: 'Đổi mật khẩu thành công',
    });
  }

  /**
   * Admin đổi mật khẩu employee (compatible với UserAuthService interface)
   * @param adminChangePasswordDto DTO chứa thông tin đổi mật khẩu bởi admin
   * @returns Success response
   */
  async adminChangePassword(
    adminChangePasswordDto: AdminChangePasswordDto,
  ): Promise<ApiResponseDto<AdminChangePasswordResponseDto>> {
    // Kiểm tra mật khẩu mới và xác nhận mật khẩu
    if (adminChangePasswordDto.newPassword !== adminChangePasswordDto.confirmPassword) {
      throw new AppException(AUTH_ERROR_CODE.PASSWORD_MISMATCH, 'Mật khẩu mới và xác nhận mật khẩu không khớp');
    }

    // Lấy employee
    const employee = await this.employeeRepository.findById(0, adminChangePasswordDto.userId); // Tìm không cần tenantId filter
    if (!employee) {
      throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND, 'Không tìm thấy thông tin employee');
    }

    // Hash mật khẩu mới
    const hashedNewPassword = this.encryptionService.hashPassword(adminChangePasswordDto.newPassword);

    // Cập nhật mật khẩu
    await this.employeeRepository.updatePassword(employee.tenantId, adminChangePasswordDto.userId, hashedNewPassword);

    return ApiResponseDto.success({
      message: 'Đổi mật khẩu cho employee thành công',
      userId: employee.id,
      userEmail: employee.email,
    });
  }

  /**
   * Reset mật khẩu employee (admin function - method gốc)
   * @param employeeId ID employee
   * @param tenantId Tenant ID
   * @param newPassword Mật khẩu mới
   * @returns Success response
   */
  async resetPasswordDirect(
    employeeId: number,
    tenantId: number,
    newPassword: string,
  ): Promise<ApiResponseDto<{ message: string }>> {
    const employee = await this.employeeRepository.findById(tenantId, employeeId);
    
    if (!employee) {
      throw new AppException(AUTH_ERROR_CODE.USER_NOT_FOUND, 'Không tìm thấy thông tin employee');
    }

    // Hash mật khẩu mới
    const hashedNewPassword = this.encryptionService.hashPassword(newPassword);

    // Cập nhật mật khẩu
    await this.employeeRepository.updatePassword(tenantId, employeeId, hashedNewPassword);

    return ApiResponseDto.success({
      message: 'Reset mật khẩu thành công',
    });
  }

  /**
   * Helper method: Map Employee entity sang UserResponseDto
   * @param employee Employee entity
   * @returns UserResponseDto
   */
  private mapEmployeeToUserResponse(employee: Employee): UserResponseDto {
    return {
      id: employee.id,
      username: employee.email, // Sử dụng email làm username
      email: employee.email,
      fullName: employee.employeeName,
      status: employee.getUserStatus(), // Convert từ accountStatus sang UserStatus
      position: employee.jobTitle, // Sử dụng jobTitle làm position
      createdAt: employee.createdAt, // Timestamp tạo tài khoản
      avatarUrl: null, // Employee entity chưa có avatarUrl field
    };
  }
}
