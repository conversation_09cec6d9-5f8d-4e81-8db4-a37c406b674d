version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: redai-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE:-ai_erp_dev}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "${DB_PORT:-5433}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - redai-dev-network

  # Redis Cache for Development
  redis-dev:
    image: redis:7-alpine
    container_name: redai-redis-dev
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6380}:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - redai-dev-network

  # Backend Application for Development (with hot reload)
  backend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: redai-backend-dev
    restart: unless-stopped
    ports:
      - "${PORT:-3001}:3000"
      - "9229:9229" # Debug port
    environment:
      NODE_ENV: development
      PORT: 3000
      DB_HOST: postgres-dev
      DB_PORT: 5432
      DB_DATABASE: ${DB_DATABASE:-ai_erp_dev}
      DB_USERNAME: ${DB_USERNAME:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-password}
      REDIS_HOST: redis-dev
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      ENABLE_TENANT_DEBUG: true
    depends_on:
      - postgres-dev
      - redis-dev
    networks:
      - redai-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    command: npm run start:debug

  # PgAdmin for Development
  pgadmin-dev:
    image: dpage/pgadmin4:latest
    container_name: redai-pgadmin-dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-dev123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-5051}:80"
    depends_on:
      - postgres-dev
    networks:
      - redai-dev-network
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  redai-dev-network:
    driver: bridge
