import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '@/modules/hrm/employees/repositories/employee.repository';
import { DepartmentRepository } from '@/modules/hrm/org-units/repositories/department.repository';
import { ReportData, ReportFilters, DateRange } from '../interfaces';

/**
 * Service xử lý các báo cáo liên quan đến nhân sự
 */
@Injectable()
export class HRReportsService {
  private readonly logger = new Logger(HRReportsService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly departmentRepository: DepartmentRepository,
  ) {}

  /**
   * Lấy dữ liệu báo cáo danh sách nhân viên
   */
  async getEmployeesReportData(
    tenantId: number,
    filters: ReportFilters,
    dateRange: DateRange
  ): Promise<ReportData> {
    try {
      this.logger.log(`<PERSON><PERSON><PERSON> dữ liệu báo cáo nhân viên cho tenant: ${tenantId}`);

      // L<PERSON>y danh sách nhân viên với filters
      const employees = await this.employeeRepository.findEmployeesForReport(
        tenantId,
        filters
      );

      // Lấy thông tin phòng ban
      const departments = await this.departmentRepository.getAllByTenantId(tenantId);
      const departmentMap = new Map(departments.map(d => [d.id, d.name]));

      // Transform data cho báo cáo
      const reportItems = employees.map(emp => ({
        employeeCode: emp.employeeCode,
        employeeName: emp.employeeName,
        email: emp.email,
        departmentName: departmentMap.get(emp.departmentId || 0) || 'Chưa phân bổ',
        position: emp.jobTitle || 'Chưa xác định',
        hireDate: emp.hireDate,
        employmentType: this.formatEmploymentType(emp.employmentType || null),
        status: this.formatEmployeeStatus(emp.status),
        salary: 0, // Salary field không có trong Employee entity
        gender: emp.gender || 'Khác',
        dateOfBirth: emp.dateOfBirth
      }));

      // Tính toán summary
      const summary = this.calculateEmployeesSummary(reportItems, departments);

      return {
        items: reportItems,
        summary,
        metadata: {
          generatedBy: 'HRReportsService',
          generatedAt: new Date(),
          dataSource: 'employees',
          version: '1.0',
          filters,
          dateRange
        }
      };

    } catch (error) {
      this.logger.error(`Lỗi lấy dữ liệu báo cáo nhân viên: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy dữ liệu báo cáo nhân viên: ${error.message}`);
    }
  }

  /**
   * Lấy dữ liệu báo cáo thống kê nhân sự
   */
  async getHRStatisticsReportData(
    tenantId: number,
    filters: ReportFilters,
    dateRange: DateRange
  ): Promise<ReportData> {
    try {
      this.logger.log(`Lấy dữ liệu thống kê nhân sự cho tenant: ${tenantId}`);

      // Lấy dữ liệu thống kê
      const employees = await this.employeeRepository.findEmployeesForReport(tenantId, filters);
      const departments = await this.departmentRepository.getAllByTenantId(tenantId);

      // Tính toán các chỉ số thống kê
      const statistics = this.calculateHRStatistics(employees, departments);

      // Tạo data cho sheet thống kê tổng quan
      const overviewItems = [
        { metric: 'Tổng số nhân viên', value: statistics.totalEmployees, percentage: 1 },
        { metric: 'Nhân viên đang làm việc', value: statistics.activeEmployees, percentage: statistics.activeEmployees / statistics.totalEmployees },
        { metric: 'Nhân viên nghỉ việc', value: statistics.inactiveEmployees, percentage: statistics.inactiveEmployees / statistics.totalEmployees },
        { metric: 'Nhân viên thử việc', value: statistics.probationEmployees, percentage: statistics.probationEmployees / statistics.totalEmployees },
        { metric: 'Nhân viên nam', value: statistics.maleEmployees, percentage: statistics.maleEmployees / statistics.totalEmployees },
        { metric: 'Nhân viên nữ', value: statistics.femaleEmployees, percentage: statistics.femaleEmployees / statistics.totalEmployees },
        { metric: 'Số phòng ban', value: statistics.totalDepartments, percentage: null }
      ];

      // Tạo data cho sheet phân bố theo phòng ban
      const departmentItems = statistics.departmentDistribution.map(dept => ({
        departmentName: dept.name,
        employeeCount: dept.count,
        percentage: dept.count / statistics.totalEmployees
      }));

      return {
        items: overviewItems,
        summary: {
          totalItems: overviewItems.length,
          calculations: {
            totalEmployees: statistics.totalEmployees,
            activeRate: statistics.activeEmployees / statistics.totalEmployees,
            genderRatio: statistics.maleEmployees / statistics.femaleEmployees
          },
          groupedData: {
            departmentDistribution: departmentItems
          },
          statistics
        },
        metadata: {
          generatedBy: 'HRReportsService',
          generatedAt: new Date(),
          dataSource: 'hr_statistics',
          version: '1.0',
          filters,
          dateRange
        }
      };

    } catch (error) {
      this.logger.error(`Lỗi lấy dữ liệu thống kê nhân sự: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy dữ liệu thống kê nhân sự: ${error.message}`);
    }
  }

  /**
   * Tính toán summary cho báo cáo nhân viên
   */
  private calculateEmployeesSummary(employees: any[], departments: any[]): any {
    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(emp => emp.status === 'Đang làm việc').length;
    const departmentCounts = employees.reduce((acc, emp) => {
      acc[emp.departmentName] = (acc[emp.departmentName] || 0) + 1;
      return acc;
    }, {});

    return {
      totalItems: totalEmployees,
      calculations: {
        totalEmployees,
        activeEmployees,
        inactiveEmployees: totalEmployees - activeEmployees,
        averageAge: this.calculateAverageAge(employees),
        departmentCount: Object.keys(departmentCounts).length
      },
      groupedData: {
        byDepartment: departmentCounts,
        byStatus: employees.reduce((acc, emp) => {
          acc[emp.status] = (acc[emp.status] || 0) + 1;
          return acc;
        }, {}),
        byGender: employees.reduce((acc, emp) => {
          acc[emp.gender] = (acc[emp.gender] || 0) + 1;
          return acc;
        }, {})
      }
    };
  }

  /**
   * Tính toán thống kê HR chi tiết
   */
  private calculateHRStatistics(employees: any[], departments: any[]): any {
    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(emp => emp.status === 'active').length;
    const inactiveEmployees = totalEmployees - activeEmployees;
    const probationEmployees = employees.filter(emp => emp.employmentType === 'PROBATION').length;
    const maleEmployees = employees.filter(emp => emp.gender === 'male').length;
    const femaleEmployees = employees.filter(emp => emp.gender === 'female').length;

    // Phân bố theo phòng ban
    const departmentMap = new Map(departments.map(d => [d.id, d.name]));
    const departmentCounts = employees.reduce((acc, emp) => {
      const deptName = departmentMap.get(emp.departmentId) || 'Chưa phân bổ';
      acc[deptName] = (acc[deptName] || 0) + 1;
      return acc;
    }, {});

    const departmentDistribution = Object.entries(departmentCounts).map(([name, count]) => ({
      name,
      count: count as number
    }));

    return {
      totalEmployees,
      activeEmployees,
      inactiveEmployees,
      probationEmployees,
      maleEmployees,
      femaleEmployees,
      totalDepartments: departments.length,
      departmentDistribution
    };
  }

  /**
   * Tính tuổi trung bình
   */
  private calculateAverageAge(employees: any[]): number {
    const employeesWithAge = employees.filter(emp => emp.dateOfBirth);
    if (employeesWithAge.length === 0) return 0;

    const totalAge = employeesWithAge.reduce((sum, emp) => {
      const age = new Date().getFullYear() - new Date(emp.dateOfBirth).getFullYear();
      return sum + age;
    }, 0);

    return Math.round(totalAge / employeesWithAge.length);
  }

  /**
   * Format employment type
   */
  private formatEmploymentType(type: string | null): string {
    if (!type) return 'Chưa xác định';

    const typeMap = {
      'FULL_TIME': 'Toàn thời gian',
      'PART_TIME': 'Bán thời gian',
      'CONTRACT': 'Hợp đồng',
      'INTERN': 'Thực tập',
      'PROBATION': 'Thử việc'
    };
    return typeMap[type] || type;
  }

  /**
   * Format employee status
   */
  private formatEmployeeStatus(status: string): string {
    const statusMap = {
      'active': 'Đang làm việc',
      'inactive': 'Nghỉ việc',
      'suspended': 'Tạm nghỉ',
      'terminated': 'Chấm dứt hợp đồng'
    };
    return statusMap[status] || status;
  }
}
