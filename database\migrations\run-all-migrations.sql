-- =====================================================
-- MASTER MIGRATION SCRIPT
-- Chạy tất cả migrations để gộp Users vào Employees
-- Ngày: 2025-06-18
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- Tạo bảng migration tracking
CREATE TABLE IF NOT EXISTS migration_history (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'running',
    started_at BIGINT DEFAULT EXTRACT(epoch FROM now()) * 1000,
    completed_at BIGINT,
    error_message TEXT
);

-- =====================================================
-- MIGRATION 001: Cập nhật cấu trúc <PERSON>LOYEES
-- =====================================================

INSERT INTO migration_history (migration_name) VALUES ('001-update-employees-structure');

DO $$
BEGIN
    -- Chạy migration 001
    RAISE NOTICE 'Starting Migration 001: Update Employees Structure';
    
    -- Backup bảng employees hiện tại
    DROP TABLE IF EXISTS employees_backup_old;
    CREATE TABLE employees_backup_old AS SELECT * FROM employees;
    
    -- Thêm các trường authentication cần thiết
    ALTER TABLE employees 
    ADD COLUMN IF NOT EXISTS account_status VARCHAR(20) DEFAULT 'PENDING',
    ADD COLUMN IF NOT EXISTS tenant_id INTEGER,
    ADD COLUMN IF NOT EXISTS employee_code VARCHAR(50),
    ADD COLUMN IF NOT EXISTS employee_name VARCHAR(255),
    ADD COLUMN IF NOT EXISTS position VARCHAR(255),
    ADD COLUMN IF NOT EXISTS department_id INTEGER,
    ADD COLUMN IF NOT EXISTS manager_id INTEGER,
    ADD COLUMN IF NOT EXISTS hire_date DATE,
    ADD COLUMN IF NOT EXISTS employment_type VARCHAR(50) DEFAULT 'FULL_TIME',
    ADD COLUMN IF NOT EXISTS salary DECIMAL(15,2),
    ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
    ADD COLUMN IF NOT EXISTS date_of_birth DATE,
    ADD COLUMN IF NOT EXISTS gender VARCHAR(10),
    ADD COLUMN IF NOT EXISTS marital_status VARCHAR(20),
    ADD COLUMN IF NOT EXISTS emergency_contact_name VARCHAR(255),
    ADD COLUMN IF NOT EXISTS emergency_contact_phone VARCHAR(20),
    ADD COLUMN IF NOT EXISTS bank_account_number VARCHAR(50),
    ADD COLUMN IF NOT EXISTS bank_name VARCHAR(255),
    ADD COLUMN IF NOT EXISTS social_insurance_number VARCHAR(50),
    ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50),
    ADD COLUMN IF NOT EXISTS notes TEXT;

    -- Cập nhật các trường hiện có
    ALTER TABLE employees 
    ALTER COLUMN email SET NOT NULL,
    ALTER COLUMN password DROP NOT NULL,
    ALTER COLUMN created_at SET NOT NULL;

    -- Xử lý dữ liệu cũ
    UPDATE employees 
    SET 
        employee_name = COALESCE(full_name, email),
        status = CASE 
            WHEN enable = true THEN 'active'
            ELSE 'inactive'
        END,
        account_status = CASE 
            WHEN enable = true THEN 'ACTIVE'
            ELSE 'INACTIVE'
        END,
        tenant_id = 1,
        employee_code = 'REDAI' || id
    WHERE employee_name IS NULL OR status IS NULL OR account_status IS NULL;

    -- Xóa các cột cũ
    ALTER TABLE employees 
    DROP COLUMN IF EXISTS full_name,
    DROP COLUMN IF EXISTS enable,
    DROP COLUMN IF EXISTS phone_number,
    DROP COLUMN IF EXISTS address;

    -- Tạo constraints
    ALTER TABLE employees 
    ADD CONSTRAINT IF NOT EXISTS employees_account_status_check 
    CHECK (account_status IN ('ACTIVE', 'INACTIVE', 'PENDING'));

    ALTER TABLE employees 
    ADD CONSTRAINT IF NOT EXISTS employees_employment_type_check 
    CHECK (employment_type IN ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN'));

    ALTER TABLE employees 
    ADD CONSTRAINT IF NOT EXISTS employees_status_check 
    CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave'));

    -- Đảm bảo tenant_id không null
    ALTER TABLE employees 
    ALTER COLUMN tenant_id SET NOT NULL;

    -- Tạo unique constraint cho employee_code
    ALTER TABLE employees 
    ADD CONSTRAINT IF NOT EXISTS employees_employee_code_unique UNIQUE (employee_code);

    -- Tạo indexes
    CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
    CREATE INDEX IF NOT EXISTS idx_employees_tenant_email ON employees(tenant_id, email);
    CREATE INDEX IF NOT EXISTS idx_employees_account_status ON employees(account_status);
    CREATE INDEX IF NOT EXISTS idx_employees_tenant_id ON employees(tenant_id);
    CREATE INDEX IF NOT EXISTS idx_employees_employee_code ON employees(employee_code);

    -- Cập nhật migration status
    UPDATE migration_history 
    SET status = 'completed', completed_at = EXTRACT(epoch FROM now()) * 1000
    WHERE migration_name = '001-update-employees-structure' AND status = 'running';

    RAISE NOTICE 'Completed Migration 001: Update Employees Structure';

EXCEPTION
    WHEN OTHERS THEN
        UPDATE migration_history 
        SET status = 'failed', error_message = SQLERRM, completed_at = EXTRACT(epoch FROM now()) * 1000
        WHERE migration_name = '001-update-employees-structure' AND status = 'running';
        RAISE;
END $$;

-- =====================================================
-- MIGRATION 002: Migrate dữ liệu từ USERS
-- =====================================================

INSERT INTO migration_history (migration_name) VALUES ('002-migrate-users-to-employees');

DO $$
BEGIN
    RAISE NOTICE 'Starting Migration 002: Migrate Users to Employees';
    
    -- Backup bảng users
    DROP TABLE IF EXISTS users_backup;
    CREATE TABLE users_backup AS SELECT * FROM users;
    
    -- Tạo bảng migration log
    DROP TABLE IF EXISTS user_employee_migration_log;
    CREATE TABLE user_employee_migration_log (
        id SERIAL PRIMARY KEY,
        old_user_id INTEGER,
        employee_id INTEGER,
        user_email VARCHAR(255),
        employee_email VARCHAR(255),
        employee_name VARCHAR(255),
        migration_method VARCHAR(50),
        migration_status VARCHAR(20) DEFAULT 'pending',
        error_message TEXT,
        created_at BIGINT DEFAULT EXTRACT(epoch FROM now()) * 1000
    );

    -- Tìm employees có email trùng với users
    INSERT INTO user_employee_migration_log (
        old_user_id, employee_id, user_email, employee_email, employee_name, migration_method
    )
    SELECT
        u.id, e.id, u.email, e.email, e.employee_name, 'update_existing'
    FROM users u
    INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

    -- Cập nhật authentication data cho employees có sẵn
    UPDATE employees e
    SET 
        password = u.password,
        account_status = CASE 
            WHEN u.is_active = true THEN 'ACTIVE'
            ELSE 'INACTIVE'
        END,
        date_of_birth = u.date_of_birth,
        gender = CASE 
            WHEN u.gender::text = 'male' THEN 'male'
            WHEN u.gender::text = 'female' THEN 'female'
            ELSE 'other'
        END,
        bank_account_number = u.account_number,
        tax_id = u.tax_code,
        updated_at = EXTRACT(epoch FROM now()) * 1000
    FROM users u
    WHERE LOWER(e.email) = LOWER(u.email);

    -- Cập nhật migration status
    UPDATE user_employee_migration_log 
    SET migration_status = 'success'
    WHERE migration_method = 'update_existing';

    -- Tạo employees mới cho users không có employee
    INSERT INTO employees (
        email, password, account_status, employee_code, employee_name, status, tenant_id,
        date_of_birth, gender, bank_account_number, tax_id, employment_type, hire_date,
        created_at, updated_at
    )
    SELECT 
        u.email,
        u.password,
        CASE WHEN u.is_active = true THEN 'ACTIVE' ELSE 'INACTIVE' END,
        'REDAI' || (
            COALESCE(
                (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
                 FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
            ) + ROW_NUMBER() OVER (ORDER BY u.id)
        ),
        COALESCE(u.full_name, u.email),
        CASE WHEN u.is_active = true THEN 'active' ELSE 'inactive' END,
        1, -- Default tenant_id
        u.date_of_birth,
        CASE 
            WHEN u.gender::text = 'male' THEN 'male'
            WHEN u.gender::text = 'female' THEN 'female'
            ELSE 'other'
        END,
        u.account_number,
        u.tax_code,
        'FULL_TIME',
        CURRENT_DATE,
        u.created_at,
        EXTRACT(epoch FROM now()) * 1000
    FROM users u
    WHERE NOT EXISTS (
        SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
    );

    -- Cập nhật migration log cho users mới tạo
    INSERT INTO user_employee_migration_log (
        old_user_id, employee_id, user_email, employee_email, employee_name, 
        migration_method, migration_status
    )
    SELECT
        u.id, e.id, u.email, e.email, e.employee_name, 'create_new', 'success'
    FROM users u
    INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email)
    WHERE NOT EXISTS (
        SELECT 1 FROM user_employee_migration_log m WHERE m.old_user_id = u.id
    );

    -- Cập nhật migration status
    UPDATE migration_history 
    SET status = 'completed', completed_at = EXTRACT(epoch FROM now()) * 1000
    WHERE migration_name = '002-migrate-users-to-employees' AND status = 'running';

    RAISE NOTICE 'Completed Migration 002: Migrate Users to Employees';

EXCEPTION
    WHEN OTHERS THEN
        UPDATE migration_history 
        SET status = 'failed', error_message = SQLERRM, completed_at = EXTRACT(epoch FROM now()) * 1000
        WHERE migration_name = '002-migrate-users-to-employees' AND status = 'running';
        RAISE;
END $$;

-- =====================================================
-- FINAL VERIFICATION
-- =====================================================

-- Kiểm tra kết quả migration
SELECT 
    'MIGRATION SUMMARY' as report_type,
    (SELECT COUNT(*) FROM users_backup) as original_users_count,
    (SELECT COUNT(*) FROM employees) as total_employees_after_migration,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'success') as successful_migrations,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'failed') as failed_migrations;

-- Kiểm tra employees có authentication data
SELECT
    'EMPLOYEES AUTH CHECK' as check_type,
    COUNT(*) as total_employees,
    COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
    COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
    COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id
FROM employees;

-- Kiểm tra migration history
SELECT * FROM migration_history ORDER BY started_at;

COMMIT;

-- Thông báo hoàn thành
SELECT 'DATABASE MIGRATION COMPLETED SUCCESSFULLY' as status;
