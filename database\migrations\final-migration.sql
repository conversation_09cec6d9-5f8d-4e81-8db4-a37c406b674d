-- =====================================================
-- FINAL MIGRATION: Gộp Users vào Employees (đơn giản)
-- Ngày: 2025-06-18
-- =====================================================

-- Backup dữ liệu
CREATE TABLE employees_backup_final AS SELECT * FROM employees;
CREATE TABLE users_backup_final AS SELECT * FROM users;

-- Thêm missing columns nếu chưa có
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'employees' AND column_name = 'tax_code') THEN
        ALTER TABLE employees ADD COLUMN tax_code VARCHAR(50);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'employees' AND column_name = 'insurance_number') THEN
        ALTER TABLE employees ADD COLUMN insurance_number VARCHAR(50);
    END IF;
END $$;

-- Cập nhật employees có email trùng với users
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.status::text = 'active' THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    date_of_birth = u.birth_date,
    gender = CASE 
        WHEN u.gender = 'male' THEN 'male'::gender_enum
        WHEN u.gender = 'female' THEN 'female'::gender_enum
        ELSE 'other'::gender_enum
    END,
    bank_account_number = u.bank_account_number,
    bank_name = u.bank_name,
    tax_code = u.tax_code,
    insurance_number = u.insurance_number,
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Tạo employees mới cho users không có employee
INSERT INTO employees (
    employee_code, employee_name, email, password, account_status, status, tenant_id,
    date_of_birth, gender, bank_account_number, bank_name, tax_code, insurance_number,
    job_title, department_id, hire_date, employment_type, created_at, updated_at
)
SELECT 
    'REDAI' || LPAD((
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    )::text, 3, '0'),
    COALESCE(u.full_name, u.email),
    u.email,
    u.password,
    CASE WHEN u.status::text = 'active' THEN 'ACTIVE' ELSE 'INACTIVE' END,
    CASE WHEN u.status::text = 'active' THEN 'active'::employee_status_enum ELSE 'inactive'::employee_status_enum END,
    COALESCE(u.tenant_id, 1),
    u.birth_date,
    CASE 
        WHEN u.gender = 'male' THEN 'male'::gender_enum
        WHEN u.gender = 'female' THEN 'female'::gender_enum
        ELSE 'other'::gender_enum
    END,
    u.bank_account_number,
    u.bank_name,
    u.tax_code,
    u.insurance_number,
    COALESCE(u.position, 'Nhân viên'),
    u.department_id,
    CURRENT_DATE,
    'full_time'::employment_type_enum,
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
);

-- Tạo mapping table để track user_id -> employee_id
CREATE TEMP TABLE user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Cập nhật user_roles
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ur.user_id = m.user_id;

-- Tạo indexes cho performance
CREATE INDEX idx_employees_email_auth ON employees(email) WHERE email IS NOT NULL;
CREATE INDEX idx_employees_account_status_auth ON employees(account_status) WHERE account_status IS NOT NULL;

-- Kiểm tra kết quả
SELECT 
    'FINAL MIGRATION COMPLETED' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees,
    (SELECT COUNT(*) FROM employees WHERE password IS NOT NULL) as employees_with_password,
    (SELECT COUNT(*) FROM employees WHERE email IS NOT NULL) as employees_with_email;

SELECT 'FINAL MIGRATION COMPLETED SUCCESSFULLY' as final_status;
