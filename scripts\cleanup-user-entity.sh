#!/bin/bash

# =====================================================
# Cleanup Script sau khi gộp User vào Employee
# Xóa các file và code không còn cần thiết
# =====================================================

echo "🧹 Starting User Entity Cleanup..."

# Tạo backup trước khi cleanup
BACKUP_DIR="backup/user-entity-cleanup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 Creating backup in $BACKUP_DIR..."

# Backup các file sẽ bị xóa hoặc thay đổi
cp -r src/modules/auth/repositories/user.repository.ts "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  user.repository.ts not found"
cp -r src/modules/hrm/employees/services/user.service.ts "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  user.service.ts not found"
cp -r src/modules/hrm/employees/controllers/user.controller.ts "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  user.controller.ts not found"

echo "✅ Backup completed"

# =====================================================
# Phase 1: Xóa UserRepository (Optional - sau khi confirm không còn sử dụng)
# =====================================================

echo "🗑️  Phase 1: UserRepository cleanup..."

# Comment: Không xóa ngay, để developer tự quyết định
echo "⚠️  UserRepository được giữ lại với deprecation warnings"
echo "   Có thể xóa sau khi confirm không còn code nào sử dụng"

# =====================================================
# Phase 2: Cleanup UserService (Optional)
# =====================================================

echo "🗑️  Phase 2: UserService cleanup..."

# Comment: Không xóa ngay, để developer tự quyết định
echo "⚠️  UserService được giữ lại với deprecation warnings"
echo "   Có thể migrate logic sang EmployeeService và xóa sau"

# =====================================================
# Phase 3: Cleanup imports và references
# =====================================================

echo "🧹 Phase 3: Cleaning up imports..."

# Tìm và list các file còn import User entity
echo "🔍 Searching for remaining User entity imports..."
grep -r "from.*user\.entity" src/ --include="*.ts" | grep -v "backup" | grep -v "node_modules" || echo "✅ No User entity imports found"

# Tìm các file còn reference UserRepository
echo "🔍 Searching for UserRepository references..."
grep -r "UserRepository" src/ --include="*.ts" | grep -v "backup" | grep -v "node_modules" | grep -v "DEPRECATED" || echo "✅ No active UserRepository references found"

# =====================================================
# Phase 4: Verify Employee entity sẵn sàng
# =====================================================

echo "✅ Phase 4: Verifying Employee entity readiness..."

# Check Employee entity có đầy đủ authentication fields
if grep -q "email.*string" src/modules/hrm/employees/entities/employee.entity.ts && \
   grep -q "password.*string" src/modules/hrm/employees/entities/employee.entity.ts && \
   grep -q "accountStatus" src/modules/hrm/employees/entities/employee.entity.ts; then
    echo "✅ Employee entity has authentication fields"
else
    echo "❌ Employee entity missing authentication fields"
    exit 1
fi

# Check Employee entity có helper methods
if grep -q "getUserStatus" src/modules/hrm/employees/entities/employee.entity.ts && \
   grep -q "canLogin" src/modules/hrm/employees/entities/employee.entity.ts; then
    echo "✅ Employee entity has helper methods"
else
    echo "❌ Employee entity missing helper methods"
    exit 1
fi

# =====================================================
# Phase 5: Generate cleanup report
# =====================================================

echo "📊 Phase 5: Generating cleanup report..."

cat > "docs/user-entity-cleanup-report.md" << EOF
# User Entity Cleanup Report

Generated: $(date)

## ✅ Completed Actions

1. **User Entity Removed**: src/modules/auth/entities/user.entity.ts
2. **Module Updates**: Removed User entity from TypeORM imports
3. **Deprecation Warnings**: Added to UserRepository and UserService
4. **Foreign Key Comments**: Updated all entities referencing user_id

## ⚠️ Pending Actions (Manual Review Required)

1. **UserRepository**: Still exists with deprecation warnings
   - Location: src/modules/auth/repositories/user.repository.ts
   - Action: Can be removed after confirming no active usage

2. **UserService**: Still exists with deprecation warnings
   - Location: src/modules/hrm/employees/services/user.service.ts
   - Action: Migrate logic to EmployeeService, then remove

3. **UserController**: Still exists with deprecation warnings
   - Location: src/modules/hrm/employees/controllers/user.controller.ts
   - Action: Migrate endpoints to EmployeeController, then remove

## 🔍 Verification Checklist

- [ ] Run tests to ensure no broken imports
- [ ] Verify authentication works with Employee entity
- [ ] Check all APIs still function correctly
- [ ] Confirm no runtime errors related to User entity

## 📦 Backup Location

Backup created at: $BACKUP_DIR

## 🚀 Next Steps

1. Run the test script: \`node scripts/test-user-employee-merge.js\`
2. Test authentication functionality
3. Remove deprecated services when ready
4. Update API documentation

EOF

echo "✅ Cleanup report generated: docs/user-entity-cleanup-report.md"

# =====================================================
# Phase 6: Final verification
# =====================================================

echo "🔍 Phase 6: Final verification..."

# Check for any remaining User entity references that might cause issues
echo "🔍 Checking for potential issues..."

# Look for any remaining User imports that aren't commented out
ACTIVE_USER_IMPORTS=$(grep -r "import.*User.*from.*user\.entity" src/ --include="*.ts" | grep -v "//" | grep -v "backup" | wc -l)
if [ "$ACTIVE_USER_IMPORTS" -gt 0 ]; then
    echo "⚠️  Found $ACTIVE_USER_IMPORTS active User entity imports"
    grep -r "import.*User.*from.*user\.entity" src/ --include="*.ts" | grep -v "//" | grep -v "backup"
else
    echo "✅ No active User entity imports found"
fi

# Check TypeScript compilation (if tsc is available)
if command -v tsc &> /dev/null; then
    echo "🔍 Checking TypeScript compilation..."
    if tsc --noEmit --skipLibCheck; then
        echo "✅ TypeScript compilation successful"
    else
        echo "⚠️  TypeScript compilation has issues"
    fi
else
    echo "⚠️  TypeScript compiler not available, skipping compilation check"
fi

echo ""
echo "🎉 User Entity Cleanup Completed!"
echo ""
echo "📋 Summary:"
echo "✅ User entity removed from codebase"
echo "✅ Module imports updated"
echo "✅ Deprecation warnings added"
echo "✅ Backup created"
echo "✅ Cleanup report generated"
echo ""
echo "🚀 Next steps:"
echo "1. Run: node scripts/test-user-employee-merge.js"
echo "2. Test authentication functionality"
echo "3. Review and remove deprecated services when ready"
echo ""
echo "📖 See docs/user-entity-cleanup-report.md for details"
