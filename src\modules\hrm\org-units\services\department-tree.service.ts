import { Injectable, Logger } from '@nestjs/common';
import { DepartmentRepository } from '../repositories/department.repository';
import { EmployeeRepository } from '@/modules/hrm/employees/repositories/employee.repository';
import { Department } from '../entities/department.entity';
import { Employee } from '@/modules/hrm/employees/entities/employee.entity';
import {
  DepartmentTreeNodeDto,
  DepartmentTreeResponseDto,
} from '../dto/department/department-tree.dto';

/**
 * Service quản lý cấu trúc cây phòng ban
 * UPDATED: Sử dụng Employee entity thay vì User entity
 */
@Injectable()
export class DepartmentTreeService {
  private readonly logger = new Logger(DepartmentTreeService.name);

  constructor(
    private readonly departmentRepository: DepartmentRepository,
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  /**
   * L<PERSON>y cấu trúc cây phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Cấu trúc cây phòng ban
   */
  async getDepartmentTree(
    tenantId: number,
  ): Promise<DepartmentTreeResponseDto> {
    // Lấy tất cả phòng ban
    const allDepartments =
      await this.departmentRepository.getAllByTenantId(tenantId);

    // Tìm tất cả các ID quản lý phòng ban
    const managerIds = allDepartments
      .filter((dept) => dept.managerId !== null)
      .map((dept) => dept.managerId) as number[];

    // Lấy thông tin của tất cả người quản lý từ Employee
    const managers: Employee[] =
      managerIds.length > 0
        ? await this.employeeRepository.findByIds(tenantId, managerIds)
        : [];

    // Tạo map người quản lý theo ID
    const managerMap = new Map<number, Employee>();
    managers.forEach((manager) => {
      managerMap.set(manager.id, manager);
    });

    // Tạo map số lượng nhân viên theo phòng ban
    const employeeCountMap = await this.getEmployeeCountByDepartment(tenantId);

    // Tạo cấu trúc cây từ danh sách phẳng
    const treeStructure = this.buildDepartmentTree(
      allDepartments,
      null,
      managerMap,
      employeeCountMap,
    );

    // Xây dựng response
    const response = new DepartmentTreeResponseDto();
    response.departments = treeStructure;
    response.totalDepartments = allDepartments.length;

    return response;
  }

  /**
   * Đếm số lượng nhân viên trong mỗi phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Map số lượng nhân viên theo ID phòng ban
   */
  private async getEmployeeCountByDepartment(
    tenantId: number,
  ): Promise<Map<number, number>> {
    // Sử dụng EmployeeRepository để lấy thống kê phân bố theo phòng ban
    const distribution = await this.employeeRepository.getDepartmentDistribution(tenantId);
    const countMap = new Map<number, number>();

    // Chuyển kết quả thống kê thành Map
    distribution.departments.forEach((dept) => {
      if (dept.departmentId) {
        countMap.set(dept.departmentId, dept.employeeCount);
      }
    });

    return countMap;
  }

  /**
   * Xây dựng cấu trúc cây phòng ban
   * @param departments Danh sách tất cả phòng ban
   * @param parentId ID phòng ban cha (hoặc null cho phòng ban gốc)
   * @param managerMap Map thông tin người quản lý (Employee)
   * @param employeeCountMap Map số lượng nhân viên theo phòng ban
   * @returns Cấu trúc cây phòng ban
   */
  private buildDepartmentTree(
    departments: Department[],
    parentId: number | null,
    managerMap: Map<number, Employee>,
    employeeCountMap: Map<number, number>,
  ): DepartmentTreeNodeDto[] {
    return departments
      .filter((dept) => dept.parentId === parentId)
      .map((dept) => {
        const node = new DepartmentTreeNodeDto();
        node.id = dept.id;
        node.name = dept.name;
        node.parentId = dept.parentId;
        node.managerId = dept.managerId;

        // Thêm tên người quản lý nếu có (sử dụng employeeName từ Employee entity)
        node.managerName =
          dept.managerId && managerMap.has(dept.managerId)
            ? managerMap.get(dept.managerId)?.employeeName || null
            : null;

        // Thêm số lượng nhân viên
        node.employeeCount = employeeCountMap.get(dept.id) || 0;

        // Đệ quy để lấy các phòng ban con
        node.children = this.buildDepartmentTree(
          departments,
          dept.id,
          managerMap,
          employeeCountMap,
        );

        return node;
      });
  }
}
