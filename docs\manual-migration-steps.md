# 🔧 Hướng Dẫn Chạy Migration Thủ Công

## 📋 Cấu Hình Database Hiện Tại

Dựa trên file `.env.development`:
```
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=redai_db
DB_SSL=false
```

## 🛠️ Cài Đặt PostgreSQL Client Tools

### Windows:
1. **Tải PostgreSQL**: https://www.postgresql.org/download/windows/
2. **Hoặc chỉ cài client tools**: https://www.enterprisedb.com/downloads/postgres-postgresql-downloads
3. **Thêm vào PATH**: `C:\Program Files\PostgreSQL\15\bin`

### Kiểm tra cài đặt:
```bash
psql --version
pg_dump --version
```

## 🚀 Các Bước Thực Hiện Migration

### Bước 1: Backup Database
```bash
# Tạo thư mục backup
mkdir backup

# Tạo backup
pg_dump -h localhost -p 5432 -U postgres -d redai_db > backup/db_backup_before_merge.sql

# Nhập password khi được yêu cầu: postgres
```

### Bước 2: Chạy Migration Script
```bash
# Chạy migration
psql -h localhost -p 5432 -U postgres -d redai_db -f database/migrations/final-user-employee-merge.sql

# Nhập password khi được yêu cầu: postgres
```

### Bước 3: Verify Migration
```bash
# Chạy test script
node scripts/test-user-employee-merge.js
```

### Bước 4: Test Application
```bash
# Start application
npm run start:dev

# Test authentication endpoints
# Test các API khác
```

## 🔍 Kiểm Tra Thủ Công

### 1. Kiểm tra Users table đã bị xóa:
```sql
-- Kết nối database
psql -h localhost -p 5432 -U postgres -d redai_db

-- Kiểm tra users table
SELECT COUNT(*) FROM users;
-- Kết quả mong đợi: ERROR: relation "users" does not exist
```

### 2. Kiểm tra Employees có authentication data:
```sql
SELECT 
    COUNT(*) as total_employees,
    COUNT(CASE WHEN email IS NOT NULL AND password IS NOT NULL THEN 1 END) as employees_with_auth,
    COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_employees
FROM employees;
```

### 3. Kiểm tra Foreign Key References:
```sql
-- Kiểm tra user_roles
SELECT COUNT(*) as total_records,
       COUNT(CASE WHEN e.id IS NOT NULL THEN 1 END) as valid_references
FROM user_roles ur
LEFT JOIN employees e ON ur.user_id = e.id;

-- Kiểm tra user_has_permissions
SELECT COUNT(*) as total_records,
       COUNT(CASE WHEN e.id IS NOT NULL THEN 1 END) as valid_references
FROM user_has_permissions uhp
LEFT JOIN employees e ON uhp.user_id = e.id;
```

### 4. Test Authentication Query:
```sql
SELECT id, email, account_status, password IS NOT NULL as has_password
FROM employees 
WHERE email IS NOT NULL 
AND account_status = 'ACTIVE'
LIMIT 5;
```

## ⚠️ Troubleshooting

### Lỗi "psql: command not found"
- Cài đặt PostgreSQL client tools
- Thêm PostgreSQL bin directory vào PATH

### Lỗi "connection refused"
- Kiểm tra PostgreSQL service đang chạy
- Kiểm tra cấu hình connection trong .env

### Lỗi "authentication failed"
- Kiểm tra username/password trong .env
- Kiểm tra pg_hba.conf configuration

### Migration thất bại
- Restore từ backup:
```bash
psql -h localhost -p 5432 -U postgres -d redai_db < backup/db_backup_before_merge.sql
```

## 🎯 Kết Quả Mong Đợi

Sau khi migration thành công:
- ✅ Users table đã bị xóa
- ✅ Users_backup table tồn tại
- ✅ Employees có authentication data
- ✅ Tất cả foreign key references đã được cập nhật
- ✅ Application authentication hoạt động với Employee entity

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong terminal
2. Kiểm tra database connection
3. Verify backup file tồn tại
4. Restore từ backup nếu cần

## 📖 Tài Liệu Tham Khảo

- [User-Employee Merge Guide](./user-employee-merge-guide.md)
- [Migration Script](../database/migrations/final-user-employee-merge.sql)
- [Test Script](../scripts/test-user-employee-merge.js)
