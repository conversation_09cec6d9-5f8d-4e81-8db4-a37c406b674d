const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Script chạy simple migration để gộp Users vào Employees
 */

async function runSimpleMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for simple migration');

    // Đọc simple migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'simple-migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting simple database migration...');
    console.log('📄 Running simple-migration.sql');

    // Chạy migration
    const result = await client.query(sqlContent);

    console.log('✅ Migration completed successfully!');

    // Verification
    console.log('\n📊 Running verification checks...');

    // Kiểm tra cấu trúc employees table
    const employeesStructure = await client.query(`
      SELECT 
        column_name, 
        data_type, 
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      AND column_name IN ('email', 'password', 'account_status', 'tenant_id', 'employee_code', 'employee_name')
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Key employees table columns:');
    console.table(employeesStructure.rows);

    // Kiểm tra dữ liệu
    const dataCheck = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id,
        COUNT(CASE WHEN employee_code IS NOT NULL THEN 1 END) as has_employee_code
      FROM employees
    `);

    console.log('\n📈 Data verification:');
    console.table(dataCheck.rows);

    // Kiểm tra sample data
    const sampleData = await client.query(`
      SELECT 
        id,
        email,
        employee_name,
        account_status,
        tenant_id,
        employee_code,
        status
      FROM employees 
      WHERE email IS NOT NULL
      LIMIT 5
    `);

    console.log('\n📋 Sample employee data:');
    console.table(sampleData.rows);

    // Kiểm tra users table vẫn tồn tại
    try {
      const usersCheck = await client.query('SELECT COUNT(*) as count FROM users LIMIT 1');
      console.log(`\n👤 Users table: ${usersCheck.rows[0].count} records (still exists)`);
    } catch (error) {
      console.log('\n👤 Users table: Not accessible');
    }

    await client.end();
    console.log('\n✅ Simple migration completed successfully!');

    // Tóm tắt kết quả
    console.log(`
🎯 SIMPLE MIGRATION SUMMARY:
- ✅ Added authentication fields to employees table
- ✅ Migrated user data to employees table
- ✅ Updated foreign key references to point to employees
- ✅ Created backup tables for safety
- ⚠️ Users table still exists (not removed in simple migration)

🚀 NEXT STEPS:
1. Test authentication with EmployeeAuthService
2. Verify all API endpoints work correctly
3. If everything works, can run cleanup to remove users table
4. Test login flow with existing credentials

🔄 BACKUP TABLES CREATED:
- employees_backup_simple (original employees data)
- users_backup_simple (original users data)

📝 NOTE: This is a simple migration that keeps users table intact.
Run cleanup migration later if needed to remove users table.
    `);

  } catch (error) {
    console.error('\n❌ Simple migration failed:', error.message);
    console.error('Full error:', error);
    
    console.log(`
🔄 ROLLBACK OPTIONS:
1. Restore from backup tables:
   - DROP TABLE employees; 
   - ALTER TABLE employees_backup_simple RENAME TO employees;
2. Check backup tables: employees_backup_simple, users_backup_simple
    `);
    
    process.exit(1);
  }
}

// Chạy migration nếu script được gọi trực tiếp
if (require.main === module) {
  console.log('🎯 Starting simple User-Employee merge migration...');
  runSimpleMigration();
}

module.exports = { runSimpleMigration };
