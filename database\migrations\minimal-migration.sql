-- =====================================================
-- MINIMAL MIGRATION: Chỉ migrate authentication data
-- Ngày: 2025-06-18
-- =====================================================

-- Backup dữ liệu
CREATE TABLE employees_backup_minimal AS SELECT * FROM employees;
CREATE TABLE users_backup_minimal AS SELECT * FROM users;

-- Cập nhật employees có email trùng với users (chỉ authentication data)
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.status::text = 'active' THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    date_of_birth = u.birth_date,
    gender = CASE 
        WHEN u.gender = 'male' THEN 'male'::gender_enum
        WHEN u.gender = 'female' THEN 'female'::gender_enum
        ELSE 'other'::gender_enum
    END,
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Tạo employees mới cho users không có employee (chỉ thông tin cơ bản)
INSERT INTO employees (
    employee_code, employee_name, email, password, account_status, status, tenant_id,
    date_of_birth, gender, job_title, hire_date, employment_type, created_at, updated_at
)
SELECT 
    'REDAI' || LPAD((
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    )::text, 3, '0'),
    COALESCE(u.full_name, u.email),
    u.email,
    u.password,
    CASE WHEN u.status::text = 'active' THEN 'ACTIVE' ELSE 'INACTIVE' END,
    CASE WHEN u.status::text = 'active' THEN 'active'::employee_status_enum ELSE 'inactive'::employee_status_enum END,
    COALESCE(u.tenant_id, 1),
    u.birth_date,
    CASE 
        WHEN u.gender = 'male' THEN 'male'::gender_enum
        WHEN u.gender = 'female' THEN 'female'::gender_enum
        ELSE 'other'::gender_enum
    END,
    COALESCE(u.position, 'Nhân viên'),
    CURRENT_DATE,
    'full_time'::employment_type_enum,
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
);

-- Tạo mapping table
CREATE TEMP TABLE user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Cập nhật user_roles
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ur.user_id = m.user_id;

-- Tạo indexes
CREATE INDEX idx_employees_email_minimal ON employees(email) WHERE email IS NOT NULL;
CREATE INDEX idx_employees_account_status_minimal ON employees(account_status) WHERE account_status IS NOT NULL;

-- Kiểm tra kết quả
SELECT 
    'MINIMAL MIGRATION COMPLETED' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees,
    (SELECT COUNT(*) FROM employees WHERE password IS NOT NULL) as employees_with_password,
    (SELECT COUNT(*) FROM employees WHERE email IS NOT NULL) as employees_with_email;

SELECT 'MINIMAL MIGRATION SUCCESS' as final_status;
