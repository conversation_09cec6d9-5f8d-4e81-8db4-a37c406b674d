# Development Dockerfile cho hot reload và debugging
FROM node:20-alpine AS development

# Cài đặt các dependencies cần thiết cho Alpine
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Tạo user non-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

WORKDIR /app

# Sao chép package files
COPY package*.json ./

# Cài đặt tất cả dependencies (bao gồm devDependencies)
RUN npm ci

# Sao chép source code
COPY . .

# Chuyển ownership cho user nestjs
RUN chown -R nestjs:nodejs /app

# <PERSON>y<PERSON>n sang user non-root
USER nestjs

# Expose ports
EXPOSE 3000 9229

# Command mặc định cho development
CMD ["npm", "run", "start:dev"]
