import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { SchemaExcelGeneratorService } from '../services/schema-excel-generator.service';
import { ReportTemplateService } from '../services/report-template.service';
import { HRReportsService } from '../services/hr-reports.service';
import { S3Service } from '@shared/services/s3.service';
import { 
  HREmployeesReportRequestDto,
  HREmployeesReportResponseDto,
  HRStatisticsReportRequestDto,
  HRStatisticsReportResponseDto,
  HRDepartmentOverviewRequestDto,
  HRDepartmentOverviewResponseDto
} from '../dto';
import { ReportType } from '../enums';
import { ExcelGenerationOptions } from '../interfaces';

/**
 * Controller chuyên biệt cho các báo cáo nhân sự
 */
@ApiTags('HR Reports')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('reports/hr')
export class HRReportsController {
  private readonly logger = new Logger(HRReportsController.name);

  constructor(
    private readonly excelGeneratorService: SchemaExcelGeneratorService,
    private readonly templateService: ReportTemplateService,
    private readonly hrReportsService: HRReportsService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo báo cáo danh sách nhân viên
   */
  @Post('employees')
  @ApiOperation({ 
    summary: 'Tạo báo cáo danh sách nhân viên',
    description: 'Tạo báo cáo Excel chứa danh sách chi tiết thông tin nhân viên với các bộ lọc tùy chỉnh'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Tạo báo cáo nhân viên thành công',
    type: HREmployeesReportResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Lỗi server khi tạo báo cáo' 
  })
  async generateEmployeesReport(
    @Body() request: HREmployeesReportRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<HREmployeesReportResponseDto> {
    try {
      this.logger.log(`Tạo báo cáo nhân viên cho tenant ${user.tenantId}`);

      // Merge filters từ request
      const filters = {
        ...request.filters,
        employmentTypes: request.employmentTypes,
        positions: request.positions,
        genders: request.genders
      };

      // Lấy dữ liệu báo cáo
      const reportData = await this.hrReportsService.getEmployeesReportData(
        user.tenantId as number,
        filters,
        request.dateRange
      );

      // Lấy template và customize nếu cần
      const schema = this.templateService.getTemplateByType(ReportType.HR_EMPLOYEES);
      
      // Customize schema dựa trên options
      if (!request.includeSalary) {
        // Remove salary column nếu không yêu cầu
        schema.sheets[0].columns = schema.sheets[0].columns.filter(col => col.key !== 'salary');
      }

      if (!request.includePersonalInfo) {
        // Remove personal info columns
        const excludeColumns = ['dateOfBirth', 'gender', 'maritalStatus'];
        schema.sheets[0].columns = schema.sheets[0].columns.filter(
          col => !excludeColumns.includes(col.key)
        );
      }

      // Generate Excel
      const options: ExcelGenerationOptions = {
        includeCharts: request.options.includeCharts ?? true,
        includeStatistics: request.options.includeStatistics ?? true,
        format: request.options.format
      };

      const result = await this.excelGeneratorService.generateFromSchema(
        reportData.items,
        schema,
        options
      );

      // Upload lên S3
      const s3Key = `reports/${result.fileName}`;
      await this.s3Service.uploadBuffer(
        s3Key,
        result.buffer!,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
      );

      const downloadUrl = await this.s3Service.getDownloadUrl(s3Key);

      const response: HREmployeesReportResponseDto = {
        success: true,
        data: {
          fileName: result.fileName,
          downloadUrl,
          fileSize: result.fileSize,
          generatedAt: result.generatedAt.toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        statistics: (reportData.summary?.calculations as any) || {
          totalEmployees: 0,
          activeEmployees: 0,
          inactiveEmployees: 0,
          departmentCount: 0,
          averageAge: 0,
          byDepartment: {},
          byGender: {},
          byEmploymentType: {}
        },
        message: 'Báo cáo danh sách nhân viên được tạo thành công'
      };

      this.logger.log(`Hoàn thành báo cáo nhân viên: ${result.fileName}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi tạo báo cáo nhân viên: ${error.message}`, error.stack);
      return {
        success: false,
        data: null as any,
        statistics: null as any,
        error: `Không thể tạo báo cáo nhân viên: ${error.message}`
      };
    }
  }

  /**
   * Tạo báo cáo thống kê nhân sự
   */
  @Post('statistics')
  @ApiOperation({ 
    summary: 'Tạo báo cáo thống kê nhân sự',
    description: 'Tạo báo cáo Excel chứa các thống kê tổng quan về nhân sự công ty'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Tạo báo cáo thống kê thành công',
    type: HRStatisticsReportResponseDto 
  })
  async generateStatisticsReport(
    @Body() request: HRStatisticsReportRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<HRStatisticsReportResponseDto> {
    try {
      this.logger.log(`Tạo báo cáo thống kê nhân sự cho tenant ${user.tenantId}`);

      // Lấy dữ liệu thống kê
      const reportData = await this.hrReportsService.getHRStatisticsReportData(
        user.tenantId as number,
        request.filters || {},
        request.dateRange
      );

      // Lấy template
      const schema = this.templateService.getTemplateByType(ReportType.HR_STATISTICS);

      // Generate Excel với multiple sheets
      const options: ExcelGenerationOptions = {
        includeCharts: (request.options.includeCharts ?? true) && (request.includeTrendAnalysis ?? true),
        includeStatistics: request.options.includeStatistics ?? true,
        format: request.options.format
      };

      const result = await this.excelGeneratorService.generateFromSchema(
        reportData.items,
        schema,
        options
      );

      // Upload lên S3
      const s3Key2 = `reports/${result.fileName}`;
      await this.s3Service.uploadBuffer(
        s3Key2,
        result.buffer!,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
      );

      const downloadUrl = await this.s3Service.getDownloadUrl(s3Key2);

      const response: HRStatisticsReportResponseDto = {
        success: true,
        data: {
          fileName: result.fileName,
          downloadUrl,
          fileSize: result.fileSize,
          generatedAt: result.generatedAt.toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        statisticsData: (reportData.summary?.statistics as any) || {
          overview: {
            totalEmployees: 0,
            growthRate: 0,
            turnoverRate: 0,
            averageTenure: 0
          },
          departmentDistribution: [],
          ageDistribution: [],
          genderDistribution: [],
          employmentTypeDistribution: []
        },
        message: 'Báo cáo thống kê nhân sự được tạo thành công'
      };

      this.logger.log(`Hoàn thành báo cáo thống kê: ${result.fileName}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi tạo báo cáo thống kê: ${error.message}`, error.stack);
      return {
        success: false,
        data: null as any,
        statisticsData: null as any,
        error: `Không thể tạo báo cáo thống kê: ${error.message}`
      };
    }
  }

  /**
   * Tạo báo cáo tổng quan phòng ban
   */
  @Post('department-overview')
  @ApiOperation({ 
    summary: 'Tạo báo cáo tổng quan phòng ban',
    description: 'Tạo báo cáo Excel chứa thông tin tổng quan của các phòng ban'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Tạo báo cáo tổng quan phòng ban thành công',
    type: HRDepartmentOverviewResponseDto 
  })
  async generateDepartmentOverviewReport(
    @Body() request: HRDepartmentOverviewRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<HRDepartmentOverviewResponseDto> {
    try {
      this.logger.log(`Tạo báo cáo tổng quan phòng ban cho tenant ${user.tenantId}`);

      // TODO: Implement department overview logic
      // Hiện tại sử dụng HR statistics data
      const reportData = await this.hrReportsService.getHRStatisticsReportData(
        user.tenantId as number,
        request.filters || {},
        request.dateRange
      );

      const schema = this.templateService.getTemplateByType(ReportType.HR_STATISTICS);

      const options: ExcelGenerationOptions = {
        includeCharts: request.options.includeCharts ?? true,
        includeStatistics: request.options.includeStatistics ?? true,
        format: request.options.format
      };

      const result = await this.excelGeneratorService.generateFromSchema(
        reportData.items,
        schema,
        options
      );

      const s3Key3 = `reports/${result.fileName}`;
      await this.s3Service.uploadBuffer(
        s3Key3,
        result.buffer!,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
      );

      const downloadUrl = await this.s3Service.getDownloadUrl(s3Key3);

      const response: HRDepartmentOverviewResponseDto = {
        success: true,
        data: {
          fileName: result.fileName,
          downloadUrl,
          fileSize: result.fileSize,
          generatedAt: result.generatedAt.toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        departmentDetails: [], // TODO: Implement department details
        message: 'Báo cáo tổng quan phòng ban được tạo thành công'
      };

      return response;

    } catch (error) {
      this.logger.error(`Lỗi tạo báo cáo tổng quan phòng ban: ${error.message}`, error.stack);
      return {
        success: false,
        data: null as any,
        departmentDetails: [],
        error: `Không thể tạo báo cáo tổng quan phòng ban: ${error.message}`
      };
    }
  }
}
