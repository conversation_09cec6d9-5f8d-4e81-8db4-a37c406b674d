import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Entity representing project members
 */
@Entity('project_members')
export class ProjectMember {
  /**
   * Unique identifier for the project member record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the project
   */
  @Column({ name: 'project_id', type: 'integer', nullable: true })
  projectId: number | null;

  /**
   * ID of the employee who is a member of the project (formerly user_id, now references Employee.id)
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Vai trò của thành viên trong dự án (admin, member, viewer).
   * Không cho phép null.
   */
  @Column({
    type: 'enum',
    enum: ProjectMemberRole,
    nullable: false,
  })
  role: ProjectMemberRole;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number = 0;
}
