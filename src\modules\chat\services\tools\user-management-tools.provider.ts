import { Injectable } from '@nestjs/common';
import { EmployeeService } from '@/modules/hrm/employees/services/employee.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { UserAuthService } from '@modules/auth/services/user-auth.service';

/**
 * User Management Tools Provider
 * Cung cấp các tools liên quan đến quản lý người dùng (cập nhật email, xóa user, v.v.)
 */
@Injectable()
export class UserManagementToolsProvider {
  constructor(
    private readonly employeeService: EmployeeService,
    private readonly userAuthService: UserAuthService,
  ) {}

  /**
   * Lấy tất cả user management tools
   */
  getTools() {
    return [
      // Cập nhật email người dùng
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Chức năng cập nhật email đã được deprecated
            return `⚠️ CHỨC NĂNG ĐÃ DEPRECATED

📧 CẬP NHẬT EMAIL NGƯỜI DÙNG:
• Chức năng này đã được chuyển sang Employee system
• Vui lòng sử dụng Employee management tools để cập nhật thông tin nhân viên
• User ID ${_args.userId} tương ứng với Employee ID ${_args.userId} (unified system)

💡 HƯỚNG DẪN:
• Sử dụng tool "Cập nhật thông tin nhân viên" thay thế
• Email được quản lý thông qua Employee entity`;
          } catch (error) {
            return `❌ Cập nhật email người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_user_email',
          description: 'Cập nhật địa chỉ email của người dùng',
          schema: z.object({
            userId: z.number().describe('ID người dùng cần cập nhật email'),
            newEmail: z
              .string()
              .email()
              .describe('Địa chỉ email mới (phải đúng định dạng email)'),
          }),
        },
      ),

      // Xóa người dùng (soft delete)
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Chức năng xóa user đã được deprecated
            return `⚠️ CHỨC NĂNG ĐÃ DEPRECATED

🗑️ XÓA NGƯỜI DÙNG:
• Chức năng này đã được chuyển sang Employee system
• Vui lòng sử dụng Employee management tools để xóa nhân viên
• User ID ${_args.userId} tương ứng với Employee ID ${_args.userId} (unified system)

💡 HƯỚNG DẪN:
• Sử dụng tool "Xóa nhân viên" thay thế
• Hoặc cập nhật trạng thái nhân viên thành TERMINATED`;
          } catch (error) {
            return `❌ Xóa người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_user',
          description:
            'Xóa người dùng (soft delete - đánh dấu là đã xóa, không xóa vĩnh viễn)',
          schema: z.object({
            userId: z.number().describe('ID người dùng cần xóa'),
          }),
        },
      ),

      // Xóa nhiều người dùng (bulk delete)
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Chức năng bulk delete đã được deprecated
            return `⚠️ CHỨC NĂNG ĐÃ DEPRECATED

🗑️ XÓA NHIỀU NGƯỜI DÙNG:
• Chức năng này đã được chuyển sang Employee system
• Vui lòng sử dụng Employee management tools để xóa nhiều nhân viên
• User IDs ${_args.userIds.join(', ')} tương ứng với Employee IDs (unified system)

💡 HƯỚNG DẪN:
• Sử dụng tool "Xóa nhiều nhân viên" thay thế
• Hoặc cập nhật trạng thái nhiều nhân viên thành TERMINATED`;


          } catch (error) {
            return `❌ Xóa nhiều người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'bulk_delete_users',
          description: 'Xóa nhiều người dùng cùng lúc (bulk soft delete)',
          schema: z.object({
            userIds: z
              .array(z.number())
              .min(1)
              .describe('Danh sách ID người dùng cần xóa (tối thiểu 1 ID)'),
          }),
        },
      ),

      // Lấy chi tiết người dùng theo ID
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Sử dụng EmployeeService để tìm employee
            const employee = await this.employeeService.findById(tenantId, _args.userId);

            return `👤 CHI TIẾT NGƯỜI DÙNG (EMPLOYEE):

🆔 THÔNG TIN CƠ BẢN:
• ID: ${employee.id}
• Email: ${employee.email}
• Mã nhân viên: ${employee.employeeCode}
• Tên nhân viên: ${employee.employeeName}

📊 TRẠNG THÁI:
• Trạng thái: ${employee.status}
• Chức vụ: ${employee.jobTitle || 'Chưa có thông tin'}
• Phòng ban ID: ${employee.departmentId || 'Chưa có thông tin'}
• Ngày tạo: ${employee.createdAt ? new Date(employee.createdAt).toLocaleDateString('vi-VN') : 'Chưa có thông tin'}

👨‍💼 THÔNG TIN NHÂN VIÊN:
• Giới tính: ${employee.gender || 'Chưa có'}
• Ngày sinh: ${employee.dateOfBirth ? new Date(employee.dateOfBirth).toLocaleDateString('vi-VN') : 'Chưa có'}

💡 LƯU Ý: Đã chuyển sang Employee system (User ID = Employee ID)`;
          } catch (error) {
            return `❌ Lấy chi tiết người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_user_detail',
          description: 'Lấy thông tin chi tiết của một người dùng theo ID',
          schema: z.object({
            userId: z.number().describe('ID người dùng cần xem chi tiết'),
          }),
        },
      ),

      // Tìm kiếm người dùng theo email
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Sử dụng EmployeeService để tìm kiếm
            const employeeQuery = {
              page: 1,
              limit: 50,
              search: _args.email,
            };

            const result = await this.employeeService.findAll(tenantId, employeeQuery);
            const matchedEmployees = result.items.filter(employee =>
              employee.email && employee.email.toLowerCase().includes(_args.email.toLowerCase())
            );

            if (matchedEmployees.length === 0) {
              return `🔍 KHÔNG TÌM THẤY NGƯỜI DÙNG:
• Không có nhân viên nào có email chứa: "${_args.email}"`;
            }

            let responseMessage = `🔍 KẾT QUẢ TÌM KIẾM NGƯỜI DÙNG (EMPLOYEES):
• Từ khóa tìm kiếm: "${_args.email}"
• Tìm thấy: ${matchedEmployees.length} nhân viên

📋 DANH SÁCH KẾT QUẢ:`;

            matchedEmployees.forEach((employee, index) => {
              responseMessage += `\n\n${index + 1}. 👤 ${employee.employeeName || 'Chưa có tên'}
   • ID: ${employee.id}
   • Email: ${employee.email}
   • Mã NV: ${employee.employeeCode || 'Chưa có'}
   • Phòng ban ID: ${employee.departmentId || 'Chưa có'}
   • Trạng thái: ${employee.status}`;
            });

            responseMessage += `\n\n💡 LƯU Ý: Đã chuyển sang Employee system (User ID = Employee ID)`;

            return responseMessage;
          } catch (error) {
            return `❌ Tìm kiếm người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'search_users_by_email',
          description: 'Tìm kiếm người dùng theo địa chỉ email (tìm kiếm gần đúng)',
          schema: z.object({
            email: z
              .string()
              .describe('Email hoặc một phần email cần tìm kiếm'),
          }),
        },
      ),

      // Đổi mật khẩu cho người dùng
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            if (!userId) {
              return `❌ Không thể xác định người dùng hiện tại`;
            }

            const changePasswordDto = {
              currentPassword: _args.currentPassword,
              newPassword: _args.newPassword,
              confirmPassword: _args.confirmPassword,
            };

            const result = await this.userAuthService.changePassword(
              userId,
              changePasswordDto,
            );

            return `✅ Đổi mật khẩu thành công!

🔐 THÔNG TIN ĐỔI MẬT KHẨU:
• Người dùng: ID ${userId}
• Thời gian: ${new Date().toLocaleString('vi-VN')}
• Trạng thái: ${result.message}

⚠️ LƯU Ý BẢO MẬT:
• Mật khẩu mới đã được mã hóa và lưu trữ an toàn
• Vui lòng đăng xuất và đăng nhập lại với mật khẩu mới
• Không chia sẻ mật khẩu với bất kỳ ai`;
          } catch (error) {
            return `❌ Đổi mật khẩu thất bại: ${error.message}

🔍 NGUYÊN NHÂN CÓ THỂ:
• Mật khẩu hiện tại không chính xác
• Mật khẩu mới và xác nhận mật khẩu không khớp
• Mật khẩu mới không đáp ứng yêu cầu bảo mật
• Lỗi hệ thống

💡 HƯỚNG DẪN:
• Kiểm tra lại mật khẩu hiện tại
• Đảm bảo mật khẩu mới và xác nhận khớp nhau
• Mật khẩu nên có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt`;
          }
        },
        {
          name: 'change_user_password',
          description: 'Đổi mật khẩu cho người dùng hiện tại (yêu cầu mật khẩu cũ để xác thực)',
          schema: z.object({
            currentPassword: z
              .string()
              .min(1)
              .describe('Mật khẩu hiện tại (để xác thực)'),
            newPassword: z
              .string()
              .min(8)
              .describe('Mật khẩu mới (tối thiểu 8 ký tự)'),
            confirmPassword: z
              .string()
              .min(8)
              .describe('Xác nhận mật khẩu mới (phải giống với mật khẩu mới)'),
          }),
        },
      ),

      // Reset mật khẩu cho người dùng (admin function)
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Tìm employee theo email để lấy thông tin
            const searchResult = await this.employeeService.findAll(tenantId, {
              page: 1,
              limit: 1,
              search: _args.email,
            });

            const employee = searchResult.items.find(e =>
              e.email && e.email.toLowerCase() === _args.email.toLowerCase()
            );

            if (!employee) {
              return `❌ Không tìm thấy nhân viên với email: ${_args.email}`;
            }

            // Gửi email reset password
            const forgotPasswordDto = {
              email: _args.email,
            };

            await this.userAuthService.forgotPassword(forgotPasswordDto);

            return `✅ Gửi email reset mật khẩu thành công!

📧 THÔNG TIN RESET MẬT KHẨU:
• Email người dùng: ${_args.email}
• Tên nhân viên: ${employee.employeeName || 'Chưa có tên'}
• ID người dùng: ${employee.id} (= Employee ID)
• Thời gian gửi: ${new Date().toLocaleString('vi-VN')}

📬 HƯỚNG DẪN:
• Email reset mật khẩu đã được gửi đến ${_args.email}
• Link reset có hiệu lực trong 1 giờ
• Người dùng cần kiểm tra hộp thư (bao gồm thư mục spam)
• Sau khi reset, người dùng có thể đăng nhập với mật khẩu mới

⚠️ LƯU Ý:
• Chỉ sử dụng chức năng này khi người dùng yêu cầu hỗ trợ
• Đảm bảo xác thực danh tính người dùng trước khi thực hiện`;
          } catch (error) {
            return `❌ Gửi email reset mật khẩu thất bại: ${error.message}

🔍 NGUYÊN NHÂN CÓ THỂ:
• Email không tồn tại trong hệ thống
• Lỗi dịch vụ email
• Lỗi hệ thống

💡 HƯỚNG DẪN:
• Kiểm tra lại địa chỉ email
• Đảm bảo người dùng đã có tài khoản trong hệ thống
• Thử lại sau vài phút`;
          }
        },
        {
          name: 'reset_user_password',
          description: 'Gửi email reset mật khẩu cho người dùng (chức năng admin)',
          schema: z.object({
            email: z
              .string()
              .email()
              .describe('Email của người dùng cần reset mật khẩu'),
          }),
        },
      ),

      // Đổi mật khẩu cho người dùng bởi admin (không cần mật khẩu cũ)
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const adminChangePasswordDto = {
              userId: _args.userId,
              newPassword: _args.newPassword,
              confirmPassword: _args.confirmPassword,
            };

            const result = await this.userAuthService.adminChangePassword(
              adminChangePasswordDto,
            );

            return `✅ Đổi mật khẩu bởi admin thành công!

🔐 THÔNG TIN ĐỔI MẬT KHẨU:
• ID người dùng: ${result.result?.userId || 'N/A'}
• Email người dùng: ${result.result?.userEmail || 'N/A'}
• Thời gian: ${new Date().toLocaleString('vi-VN')}
• Trạng thái: ${result.result?.message || 'Thành công'}

⚠️ LƯU Ý BẢO MẬT:
• Mật khẩu mới đã được mã hóa và lưu trữ an toàn
• Người dùng cần đăng xuất và đăng nhập lại với mật khẩu mới
• Thao tác này được thực hiện bởi admin, không cần mật khẩu cũ
• Hãy thông báo cho người dùng về việc thay đổi mật khẩu

🔒 KHUYẾN NGHỊ:
• Yêu cầu người dùng đổi mật khẩu ngay sau lần đăng nhập đầu tiên
• Đảm bảo mật khẩu mới đủ mạnh và bảo mật
• Ghi lại thao tác này trong log hệ thống`;
          } catch (error) {
            return `❌ Đổi mật khẩu bởi admin thất bại: ${error.message}

🔍 NGUYÊN NHÂN CÓ THỂ:
• ID người dùng không tồn tại
• Mật khẩu mới và xác nhận mật khẩu không khớp
• Mật khẩu mới không đáp ứng yêu cầu bảo mật
• Lỗi hệ thống hoặc cơ sở dữ liệu

💡 HƯỚNG DẪN:
• Kiểm tra lại ID người dùng có tồn tại không
• Đảm bảo mật khẩu mới và xác nhận khớp nhau
• Mật khẩu nên có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt
• Thử lại sau vài phút nếu là lỗi hệ thống`;
          }
        },
        {
          name: 'admin_change_user_password',
          description: 'Đổi mật khẩu cho người dùng bởi admin (không cần mật khẩu cũ) - chức năng admin',
          schema: z.object({
            userId: z
              .number()
              .positive()
              .describe('ID người dùng cần đổi mật khẩu'),
            newPassword: z
              .string()
              .min(8)
              .describe('Mật khẩu mới (tối thiểu 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt)'),
            confirmPassword: z
              .string()
              .min(8)
              .describe('Xác nhận mật khẩu mới (phải giống với mật khẩu mới)'),
          }),
        },
      ),
    ];
  }
}
