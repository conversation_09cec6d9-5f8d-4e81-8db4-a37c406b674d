const { Client } = require('pg');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function checkCurrentStructure() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Kiểm tra cấu trúc employees table
    console.log('\n📊 EMPLOYEES TABLE STRUCTURE:');
    const employeesStructure = await client.query(`
      SELECT 
        column_name, 
        data_type, 
        is_nullable, 
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      ORDER BY ordinal_position
    `);
    
    console.table(employeesStructure.rows);

    // <PERSON><PERSON>m tra cấu trúc users table
    console.log('\n📊 USERS TABLE STRUCTURE:');
    const usersStructure = await client.query(`
      SELECT 
        column_name, 
        data_type, 
        is_nullable, 
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.table(usersStructure.rows);

    // Kiểm tra sample data
    console.log('\n📋 SAMPLE EMPLOYEES DATA:');
    const sampleEmployees = await client.query(`
      SELECT * FROM employees LIMIT 3
    `);
    console.table(sampleEmployees.rows);

    console.log('\n📋 SAMPLE USERS DATA:');
    const sampleUsers = await client.query(`
      SELECT id, email, full_name, is_active FROM users LIMIT 3
    `);
    console.table(sampleUsers.rows);

    await client.end();
    console.log('\n✅ Structure check completed');

  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

if (require.main === module) {
  checkCurrentStructure();
}

module.exports = { checkCurrentStructure };
