# =====================================================
# PowerShell Script thực hiện tất cả các bước migration User-Employee
# =====================================================

param(
    [switch]$SkipBackup = $false,
    [switch]$SkipConfirmation = $false
)

Write-Host "🚀 Starting User-Employee Migration Process..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Yellow

# Load environment variables from .env.development
$envFile = ".env.development"
if (Test-Path $envFile) {
    Get-Content $envFile | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✅ Loaded $envFile" -ForegroundColor Green
} elseif (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✅ Loaded .env" -ForegroundColor Green
} else {
    Write-Host "❌ No .env file found!" -ForegroundColor Red
    exit 1
}

# Get database configuration
$DB_HOST = if ($env:DB_HOST) { $env:DB_HOST } else { "localhost" }
$DB_PORT = if ($env:DB_PORT) { $env:DB_PORT } else { "5432" }
$DB_DATABASE = if ($env:DB_DATABASE) { $env:DB_DATABASE } else { "redai_db" }
$DB_USERNAME = if ($env:DB_USERNAME) { $env:DB_USERNAME } else { "postgres" }
$DB_PASSWORD = if ($env:DB_PASSWORD) { $env:DB_PASSWORD } else { "postgres" }
$DB_SSL = if ($env:DB_SSL) { $env:DB_SSL } else { "false" }

# Display database configuration
Write-Host ""
Write-Host "📋 Database Configuration:" -ForegroundColor Cyan
Write-Host "  Host: $DB_HOST" -ForegroundColor White
Write-Host "  Port: $DB_PORT" -ForegroundColor White
Write-Host "  Database: $DB_DATABASE" -ForegroundColor White
Write-Host "  Username: $DB_USERNAME" -ForegroundColor White
Write-Host "  SSL: $DB_SSL" -ForegroundColor White
Write-Host ""

# Confirm before proceeding
if (-not $SkipConfirmation) {
    $confirmation = Read-Host "🔍 Do you want to proceed with migration? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "❌ Migration cancelled by user" -ForegroundColor Red
        exit 1
    }
}

# =====================================================
# Step 1: Test Database Connection
# =====================================================

Write-Host "🔗 Step 1: Testing database connection..." -ForegroundColor Yellow

if (Get-Command node -ErrorAction SilentlyContinue) {
    if (Test-Path "scripts/test-db-connection.js") {
        try {
            node scripts/test-db-connection.js
            if ($LASTEXITCODE -ne 0) {
                Write-Host "❌ Database connection failed!" -ForegroundColor Red
                exit 1
            }
        } catch {
            Write-Host "❌ Database connection test failed: $_" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠️  test-db-connection.js not found, skipping connection test" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Node.js not found, skipping connection test" -ForegroundColor Yellow
}

# =====================================================
# Step 2: Create Database Backup
# =====================================================

Write-Host ""
Write-Host "💾 Step 2: Creating database backup..." -ForegroundColor Yellow

if (-not $SkipBackup) {
    $backupDir = "backup"
    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir | Out-Null
    }

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$backupDir/db_backup_before_user_employee_merge_$timestamp.sql"

    if (Get-Command pg_dump -ErrorAction SilentlyContinue) {
        Write-Host "📦 Creating backup: $backupFile" -ForegroundColor Cyan
        
        # Set environment variable for password
        $env:PGPASSWORD = $DB_PASSWORD
        
        try {
            & pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE > $backupFile
            
            if ($LASTEXITCODE -eq 0) {
                $backupSize = (Get-Item $backupFile).Length / 1MB
                Write-Host "✅ Backup created successfully: $backupFile" -ForegroundColor Green
                Write-Host "📊 Backup size: $([math]::Round($backupSize, 2)) MB" -ForegroundColor Cyan
            } else {
                Write-Host "❌ Backup failed!" -ForegroundColor Red
                exit 1
            }
        } catch {
            Write-Host "❌ Backup failed: $_" -ForegroundColor Red
            exit 1
        } finally {
            Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        }
    } else {
        Write-Host "⚠️  pg_dump not found, skipping backup" -ForegroundColor Yellow
        Write-Host "   Please create manual backup before proceeding" -ForegroundColor Yellow
        
        if (-not $SkipConfirmation) {
            $continueWithoutBackup = Read-Host "   Continue without backup? (y/N)"
            if ($continueWithoutBackup -ne 'y' -and $continueWithoutBackup -ne 'Y') {
                Write-Host "❌ Migration cancelled" -ForegroundColor Red
                exit 1
            }
        }
    }
} else {
    Write-Host "⚠️  Backup skipped by user request" -ForegroundColor Yellow
}

# =====================================================
# Step 3: Run Migration Script
# =====================================================

Write-Host ""
Write-Host "🔄 Step 3: Running migration script..." -ForegroundColor Yellow

$migrationFile = "database/migrations/final-user-employee-merge.sql"

if (-not (Test-Path $migrationFile)) {
    Write-Host "❌ Migration file not found: $migrationFile" -ForegroundColor Red
    exit 1
}

if (Get-Command psql -ErrorAction SilentlyContinue) {
    Write-Host "📝 Executing migration: $migrationFile" -ForegroundColor Cyan
    
    # Set environment variable for password
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        & psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -f $migrationFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migration executed successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Migration failed!" -ForegroundColor Red
            if ($backupFile) {
                Write-Host "🔄 You can restore from backup: $backupFile" -ForegroundColor Yellow
            }
            exit 1
        }
    } catch {
        Write-Host "❌ Migration failed: $_" -ForegroundColor Red
        exit 1
    } finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "❌ psql not found!" -ForegroundColor Red
    Write-Host "   Please install PostgreSQL client tools" -ForegroundColor Yellow
    exit 1
}

# =====================================================
# Step 4: Run Verification Tests
# =====================================================

Write-Host ""
Write-Host "🧪 Step 4: Running verification tests..." -ForegroundColor Yellow

if (Test-Path "scripts/test-user-employee-merge.js") {
    try {
        node scripts/test-user-employee-merge.js
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ All verification tests passed!" -ForegroundColor Green
        } else {
            Write-Host "❌ Some verification tests failed!" -ForegroundColor Red
            Write-Host "   Please check the output above" -ForegroundColor Yellow
            exit 1
        }
    } catch {
        Write-Host "❌ Verification tests failed: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "⚠️  Test script not found: scripts/test-user-employee-merge.js" -ForegroundColor Yellow
}

# =====================================================
# Step 5: Run Cleanup (Optional)
# =====================================================

Write-Host ""
Write-Host "🧹 Step 5: Running cleanup..." -ForegroundColor Yellow

if (Test-Path "scripts/cleanup-user-entity.sh") {
    try {
        # Run bash script on Windows (if available)
        if (Get-Command bash -ErrorAction SilentlyContinue) {
            bash scripts/cleanup-user-entity.sh
        } else {
            Write-Host "⚠️  Bash not available, skipping cleanup script" -ForegroundColor Yellow
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Cleanup completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Cleanup completed with warnings" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  Cleanup failed: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Cleanup script not found: scripts/cleanup-user-entity.sh" -ForegroundColor Yellow
}

# =====================================================
# Final Summary
# =====================================================

Write-Host ""
Write-Host "🎉 Migration Process Completed!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
if ($backupFile) {
    Write-Host "✅ Database backup created: $backupFile" -ForegroundColor Green
}
Write-Host "✅ Migration script executed successfully" -ForegroundColor Green
Write-Host "✅ Verification tests passed" -ForegroundColor Green
Write-Host "✅ Cleanup completed" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Test your application authentication" -ForegroundColor White
Write-Host "2. Verify all APIs are working correctly" -ForegroundColor White
Write-Host "3. Monitor for any issues" -ForegroundColor White
Write-Host ""
Write-Host "📖 For more details, see: docs/user-employee-merge-guide.md" -ForegroundColor Cyan
Write-Host ""
if ($backupFile) {
    Write-Host "⚠️  If you encounter issues, you can restore from backup:" -ForegroundColor Yellow
    Write-Host "   psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE < $backupFile" -ForegroundColor White
}
