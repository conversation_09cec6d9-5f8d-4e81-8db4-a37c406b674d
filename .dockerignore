# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
coverage

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Test files
test
tests
*.test.js
*.test.ts
*.spec.js
*.spec.ts
jest.config.js
coverage

# Documentation
docs
*.md

# Scripts
scripts
*.sh
*.ps1

# Backup files
backup
*.backup
*.bak

# Log files
logs
*.log

# Temporary files
tmp
temp
.tmp

# Database files
*.db
*.sqlite
*.sqlite3

# Development files
.eslintrc*
.prettierrc*
tsconfig.json
tsconfig.build.json
nest-cli.json

# Package lock files (keep package-lock.json for production builds)
yarn.lock
pnpm-lock.yaml

# Misc
.nyc_output
.cache
.parcel-cache
