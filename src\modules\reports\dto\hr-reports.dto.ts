import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsArray, IsString, IsEnum } from 'class-validator';
import { BaseReportRequestDto, BaseReportResponseDto } from './base-report.dto';

/**
 * DTO cho request báo cáo danh sách nhân viên
 */
export class HREmployeesReportRequestDto extends BaseReportRequestDto {
  @ApiPropertyOptional({
    description: 'Lọc theo loại hợp đồng',
    type: [String],
    example: ['FULL_TIME', 'PART_TIME', 'CONTRACT']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  employmentTypes?: string[];

  @ApiPropertyOptional({
    description: 'Lọc theo chức vụ',
    type: [String],
    example: ['Manager', 'Developer', 'Tester']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  positions?: string[];

  @ApiPropertyOptional({
    description: '<PERSON>ọc theo giới tính',
    type: [String],
    example: ['male', 'female']
  })
  @IsOptional()
  @IsArray()
  @IsEnum(['male', 'female', 'other'], { each: true })
  genders?: string[];

  @ApiPropertyOptional({
    description: 'Bao gồm thông tin lương không',
    default: false
  })
  @IsOptional()
  includeSalary?: boolean = false;

  @ApiPropertyOptional({
    description: 'Bao gồm thông tin cá nhân không',
    default: true
  })
  @IsOptional()
  includePersonalInfo?: boolean = true;
}

/**
 * DTO cho response báo cáo danh sách nhân viên
 */
export class HREmployeesReportResponseDto extends BaseReportResponseDto {
  @ApiProperty({
    description: 'Thống kê tổng quan',
    example: {
      totalEmployees: 150,
      activeEmployees: 145,
      inactiveEmployees: 5,
      departmentCount: 8,
      averageAge: 28.5
    }
  })
  statistics: {
    totalEmployees: number;
    activeEmployees: number;
    inactiveEmployees: number;
    departmentCount: number;
    averageAge: number;
    byDepartment: Record<string, number>;
    byGender: Record<string, number>;
    byEmploymentType: Record<string, number>;
  };
}

/**
 * DTO cho request báo cáo thống kê nhân sự
 */
export class HRStatisticsReportRequestDto extends BaseReportRequestDto {
  @ApiPropertyOptional({
    description: 'Loại thống kê cần tạo',
    type: [String],
    example: ['overview', 'department_distribution', 'age_distribution', 'tenure_analysis']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  statisticsTypes?: string[];

  @ApiPropertyOptional({
    description: 'Bao gồm biểu đồ phân tích xu hướng',
    default: true
  })
  @IsOptional()
  includeTrendAnalysis?: boolean = true;

  @ApiPropertyOptional({
    description: 'So sánh với kỳ trước',
    default: false
  })
  @IsOptional()
  compareWithPrevious?: boolean = false;
}

/**
 * DTO cho response báo cáo thống kê nhân sự
 */
export class HRStatisticsReportResponseDto extends BaseReportResponseDto {
  @ApiProperty({
    description: 'Dữ liệu thống kê chi tiết',
    example: {
      overview: {
        totalEmployees: 150,
        growthRate: 5.2,
        turnoverRate: 2.1,
        averageTenure: 2.3
      },
      departmentDistribution: [
        { department: 'IT', count: 45, percentage: 30 },
        { department: 'Sales', count: 30, percentage: 20 }
      ],
      ageDistribution: [
        { ageGroup: '20-25', count: 25, percentage: 16.7 },
        { ageGroup: '26-30', count: 60, percentage: 40 }
      ]
    }
  })
  statisticsData: {
    overview: {
      totalEmployees: number;
      growthRate: number;
      turnoverRate: number;
      averageTenure: number;
    };
    departmentDistribution: Array<{
      department: string;
      count: number;
      percentage: number;
    }>;
    ageDistribution: Array<{
      ageGroup: string;
      count: number;
      percentage: number;
    }>;
    genderDistribution: Array<{
      gender: string;
      count: number;
      percentage: number;
    }>;
    employmentTypeDistribution: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
  };
}

/**
 * DTO cho request báo cáo tổng quan phòng ban
 */
export class HRDepartmentOverviewRequestDto extends BaseReportRequestDto {
  @ApiPropertyOptional({
    description: 'Bao gồm thông tin quản lý',
    default: true
  })
  @IsOptional()
  includeManagementInfo?: boolean = true;

  @ApiPropertyOptional({
    description: 'Bao gồm phân tích hiệu suất',
    default: false
  })
  @IsOptional()
  includePerformanceAnalysis?: boolean = false;

  @ApiPropertyOptional({
    description: 'Bao gồm thống kê chi phí',
    default: false
  })
  @IsOptional()
  includeCostAnalysis?: boolean = false;
}

/**
 * DTO cho response báo cáo tổng quan phòng ban
 */
export class HRDepartmentOverviewResponseDto extends BaseReportResponseDto {
  @ApiProperty({
    description: 'Thông tin chi tiết từng phòng ban',
    example: [
      {
        departmentName: 'IT Department',
        managerName: 'Nguyễn Văn A',
        totalEmployees: 45,
        activeEmployees: 43,
        averageAge: 28.5,
        averageTenure: 2.1,
        budgetUtilization: 85.2
      }
    ]
  })
  departmentDetails: Array<{
    departmentName: string;
    managerName: string;
    totalEmployees: number;
    activeEmployees: number;
    averageAge: number;
    averageTenure: number;
    budgetUtilization?: number;
    performanceScore?: number;
  }>;
}
