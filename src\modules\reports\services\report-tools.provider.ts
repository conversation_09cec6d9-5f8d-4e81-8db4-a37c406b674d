import { Injectable, Logger } from '@nestjs/common';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { RunnableConfig } from '@langchain/core/runnables';
import { SchemaExcelGeneratorService } from './schema-excel-generator.service';
import { ReportTemplateService } from './report-template.service';
import { HRReportsService } from './hr-reports.service';
import { AttendanceReportsService } from './attendance-reports.service';
import { S3Service } from '@shared/services/s3.service';
import { ReportType } from '../enums';
import { ExcelGenerationOptions } from '../interfaces';

/**
 * Provider cung cấp tools cho chat AI để tạo báo cáo
 */
@Injectable()
export class ReportToolsProvider {
  private readonly logger = new Logger(ReportToolsProvider.name);

  constructor(
    private readonly excelGeneratorService: SchemaExcelGeneratorService,
    private readonly templateService: ReportTemplateService,
    private readonly hrReportsService: HRReportsService,
    private readonly attendanceReportsService: AttendanceReportsService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Lấy danh sách tools cho chat AI
   */
  getTools() {
    return [
      this.createHREmployeesReportTool(),
      this.createHRStatisticsReportTool(),
      this.createAttendanceDetailsReportTool(),
      this.createAttendanceSummaryReportTool(),
      this.getReportTypesTool(),
    ];
  }

  /**
   * Tool tạo báo cáo danh sách nhân viên
   */
  private createHREmployeesReportTool() {
    return tool(
      async (args, config: RunnableConfig): Promise<string> => {
        try {
          const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
          const userId = parseInt(config?.configurable?.['userId'] || '0');

          this.logger.log(`Tạo báo cáo nhân viên qua chat cho tenant ${tenantId}`);

          // Prepare filters
          const filters = {
            departmentIds: args.departmentIds,
            employeeIds: args.employeeIds,
            status: args.status,
            employmentTypes: args.employmentTypes,
            positions: args.positions,
            genders: args.genders,
          };

          // Prepare date range
          const dateRange = {
            startDate: args.startDate || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
            endDate: args.endDate || new Date().toISOString().split('T')[0],
          };

          // Get report data
          const reportData = await this.hrReportsService.getEmployeesReportData(
            tenantId,
            filters,
            dateRange
          );

          // Get template
          const schema = this.templateService.getTemplateByType(ReportType.HR_EMPLOYEES);

          // Generate Excel
          const options: ExcelGenerationOptions = {
            includeCharts: args.includeCharts ?? true,
            includeStatistics: args.includeStatistics ?? true,
            format: 'xlsx'
          };

          const result = await this.excelGeneratorService.generateFromSchema(
            reportData.items,
            schema,
            options
          );

          // Upload to S3
          const s3Key = `reports/${result.fileName}`;
          await this.s3Service.uploadBuffer(
            s3Key,
            result.buffer!,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
          );

          const downloadUrl = await this.s3Service.getDownloadUrl(s3Key);

          return `✅ Báo cáo danh sách nhân viên đã được tạo thành công!

📊 **Thông tin báo cáo:**
- Tên file: ${result.fileName}
- Kích thước: ${(result.fileSize / 1024).toFixed(2)} KB
- Số lượng nhân viên: ${reportData.items.length}
- Thời gian tạo: ${new Date().toLocaleString('vi-VN')}

🔗 **Link download:** ${downloadUrl}

⏰ Link sẽ hết hạn sau 24 giờ.

📈 **Thống kê tóm tắt:**
- Tổng nhân viên: ${reportData.summary?.calculations?.totalEmployees || 0}
- Nhân viên đang làm việc: ${reportData.summary?.calculations?.activeEmployees || 0}
- Tuổi trung bình: ${reportData.summary?.calculations?.averageAge || 0} tuổi`;

        } catch (error) {
          this.logger.error(`Lỗi tạo báo cáo nhân viên qua chat: ${error.message}`, error.stack);
          return `❌ Không thể tạo báo cáo nhân viên: ${error.message}`;
        }
      },
      {
        name: 'create_hr_employees_report',
        description: 'Tạo báo cáo danh sách nhân viên Excel với các bộ lọc tùy chỉnh',
        schema: z.object({
          departmentIds: z.array(z.number()).optional().describe('Danh sách ID phòng ban cần lọc'),
          employeeIds: z.array(z.number()).optional().describe('Danh sách ID nhân viên cụ thể'),
          status: z.array(z.string()).optional().describe('Trạng thái nhân viên (active, inactive)'),
          employmentTypes: z.array(z.string()).optional().describe('Loại hợp đồng (FULL_TIME, PART_TIME, CONTRACT)'),
          positions: z.array(z.string()).optional().describe('Chức vụ cần lọc'),
          genders: z.array(z.string()).optional().describe('Giới tính (male, female, other)'),
          startDate: z.string().optional().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().optional().describe('Ngày kết thúc (YYYY-MM-DD)'),
          includeCharts: z.boolean().optional().describe('Có bao gồm biểu đồ không'),
          includeStatistics: z.boolean().optional().describe('Có bao gồm thống kê không'),
        }),
      }
    );
  }

  /**
   * Tool tạo báo cáo thống kê nhân sự
   */
  private createHRStatisticsReportTool() {
    return tool(
      async (args, config: RunnableConfig): Promise<string> => {
        try {
          const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

          // Similar implementation for HR statistics report
          const filters = {
            departmentIds: args.departmentIds,
            employeeIds: args.employeeIds,
          };

          const dateRange = {
            startDate: args.startDate || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
            endDate: args.endDate || new Date().toISOString().split('T')[0],
          };

          const reportData = await this.hrReportsService.getHRStatisticsReportData(
            tenantId,
            filters,
            dateRange
          );

          const schema = this.templateService.getTemplateByType(ReportType.HR_STATISTICS);

          const options: ExcelGenerationOptions = {
            includeCharts: args.includeCharts ?? true,
            includeStatistics: true,
            format: 'xlsx'
          };

          const result = await this.excelGeneratorService.generateFromSchema(
            reportData.items,
            schema,
            options
          );

          const s3Key2 = `reports/${result.fileName}`;
          await this.s3Service.uploadBuffer(
            s3Key2,
            result.buffer!,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
          );

          const downloadUrl = await this.s3Service.getDownloadUrl(s3Key2);

          return `✅ Báo cáo thống kê nhân sự đã được tạo thành công!

📊 **Thông tin báo cáo:**
- Tên file: ${result.fileName}
- Kích thước: ${(result.fileSize / 1024).toFixed(2)} KB
- Thời gian tạo: ${new Date().toLocaleString('vi-VN')}

🔗 **Link download:** ${downloadUrl}

📈 **Thống kê chính:**
- Tổng nhân viên: ${reportData.summary?.statistics?.totalEmployees || 0}
- Số phòng ban: ${reportData.summary?.statistics?.totalDepartments || 0}
- Nhân viên nam: ${reportData.summary?.statistics?.maleEmployees || 0}
- Nhân viên nữ: ${reportData.summary?.statistics?.femaleEmployees || 0}`;

        } catch (error) {
          this.logger.error(`Lỗi tạo báo cáo thống kê nhân sự: ${error.message}`, error.stack);
          return `❌ Không thể tạo báo cáo thống kê nhân sự: ${error.message}`;
        }
      },
      {
        name: 'create_hr_statistics_report',
        description: 'Tạo báo cáo thống kê nhân sự với biểu đồ và phân tích',
        schema: z.object({
          departmentIds: z.array(z.number()).optional().describe('Danh sách ID phòng ban'),
          employeeIds: z.array(z.number()).optional().describe('Danh sách ID nhân viên'),
          startDate: z.string().optional().describe('Ngày bắt đầu'),
          endDate: z.string().optional().describe('Ngày kết thúc'),
          includeCharts: z.boolean().optional().describe('Bao gồm biểu đồ'),
        }),
      }
    );
  }

  /**
   * Tool tạo báo cáo chấm công chi tiết
   */
  private createAttendanceDetailsReportTool() {
    return tool(
      async (args, config: RunnableConfig): Promise<string> => {
        try {
          const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

          const filters = {
            employeeIds: args.employeeIds,
            departmentIds: args.departmentIds,
            status: args.status,
          };

          const dateRange = {
            startDate: args.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
            endDate: args.endDate || new Date().toISOString().split('T')[0],
          };

          const reportData = await this.attendanceReportsService.getAttendanceDetailsReportData(
            tenantId,
            filters,
            dateRange
          );

          const schema = this.templateService.getTemplateByType(ReportType.ATTENDANCE_DETAILS);

          const options: ExcelGenerationOptions = {
            includeCharts: args.includeCharts ?? true,
            includeStatistics: true,
            format: 'xlsx'
          };

          const result = await this.excelGeneratorService.generateFromSchema(
            reportData.items,
            schema,
            options
          );

          const s3Key3 = `reports/${result.fileName}`;
          await this.s3Service.uploadBuffer(
            s3Key3,
            result.buffer!,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
          );

          const downloadUrl = await this.s3Service.getDownloadUrl(s3Key3);

          return `✅ Báo cáo chấm công chi tiết đã được tạo thành công!

📊 **Thông tin báo cáo:**
- Tên file: ${result.fileName}
- Kích thước: ${(result.fileSize / 1024).toFixed(2)} KB
- Số bản ghi: ${reportData.items.length}
- Khoảng thời gian: ${dateRange.startDate} đến ${dateRange.endDate}

🔗 **Link download:** ${downloadUrl}

📈 **Thống kê chấm công:**
- Tổng số bản ghi: ${reportData.summary?.calculations?.totalRecords || 0}
- Có mặt: ${reportData.summary?.calculations?.presentRecords || 0}
- Đi muộn: ${reportData.summary?.calculations?.lateRecords || 0}
- Tỷ lệ chấm công: ${reportData.summary?.calculations?.attendanceRate || 0}%`;

        } catch (error) {
          this.logger.error(`Lỗi tạo báo cáo chấm công: ${error.message}`, error.stack);
          return `❌ Không thể tạo báo cáo chấm công: ${error.message}`;
        }
      },
      {
        name: 'create_attendance_details_report',
        description: 'Tạo báo cáo chấm công chi tiết theo nhân viên và thời gian',
        schema: z.object({
          employeeIds: z.array(z.number()).optional().describe('Danh sách ID nhân viên'),
          departmentIds: z.array(z.number()).optional().describe('Danh sách ID phòng ban'),
          status: z.array(z.string()).optional().describe('Trạng thái chấm công'),
          startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
          includeCharts: z.boolean().optional().describe('Bao gồm biểu đồ'),
        }),
      }
    );
  }

  /**
   * Tool tạo báo cáo tổng hợp chấm công
   */
  private createAttendanceSummaryReportTool() {
    return tool(
      async (args, config: RunnableConfig): Promise<string> => {
        try {
          const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

          const filters = {
            employeeIds: args.employeeIds,
            departmentIds: args.departmentIds,
          };

          const dateRange = {
            startDate: args.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
            endDate: args.endDate || new Date().toISOString().split('T')[0],
          };

          const reportData = await this.attendanceReportsService.getAttendanceSummaryReportData(
            tenantId,
            filters,
            dateRange
          );

          const schema = this.templateService.getTemplateByType(ReportType.ATTENDANCE_SUMMARY);

          const result = await this.excelGeneratorService.generateFromSchema(
            reportData.items,
            schema,
            { includeCharts: true, includeStatistics: true, format: 'xlsx' }
          );

          const s3Key4 = `reports/${result.fileName}`;
          await this.s3Service.uploadBuffer(
            s3Key4,
            result.buffer!,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' as any
          );

          const downloadUrl = await this.s3Service.getDownloadUrl(s3Key4);

          return `✅ Báo cáo tổng hợp chấm công đã được tạo thành công!

📊 **Link download:** ${downloadUrl}

📈 **Thống kê:** ${reportData.summary?.calculations?.totalEmployees || 0} nhân viên, tỷ lệ chấm công trung bình: ${reportData.summary?.calculations?.averageAttendanceRate || 0}%`;

        } catch (error) {
          return `❌ Không thể tạo báo cáo tổng hợp chấm công: ${error.message}`;
        }
      },
      {
        name: 'create_attendance_summary_report',
        description: 'Tạo báo cáo tổng hợp chấm công theo nhân viên',
        schema: z.object({
          employeeIds: z.array(z.number()).optional(),
          departmentIds: z.array(z.number()).optional(),
          startDate: z.string().describe('Ngày bắt đầu'),
          endDate: z.string().describe('Ngày kết thúc'),
        }),
      }
    );
  }

  /**
   * Tool lấy danh sách loại báo cáo
   */
  private getReportTypesTool() {
    return tool(
      async (): Promise<string> => {
        const reportTypes = [
          '📊 **Báo cáo Nhân sự:**',
          '- Danh sách nhân viên (hr_employees)',
          '- Thống kê nhân sự (hr_statistics)',
          '',
          '⏰ **Báo cáo Chấm công:**',
          '- Chấm công chi tiết (attendance_details)',
          '- Tổng hợp chấm công (attendance_summary)',
          '',
          '🎯 **Báo cáo OKR:** (Đang phát triển)',
          '- Tiến độ OKR (okr_progress)',
          '',
          '📋 **Báo cáo Dự án:** (Đang phát triển)',
          '- Tiến độ dự án (project_progress)',
        ];

        return reportTypes.join('\n');
      },
      {
        name: 'get_available_report_types',
        description: 'Lấy danh sách các loại báo cáo có sẵn trong hệ thống',
        schema: z.object({}),
      }
    );
  }
}
