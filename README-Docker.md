# Docker Setup cho RedAI Backend

## Tổng quan

Dự án này bao gồm các file Docker để chạy ứng dụng NestJS backend cùng với PostgreSQL và Redis.

## Cấu trúc Files

- `Dockerfile` - Production build với multi-stage
- `Dockerfile.dev` - Development build với hot reload
- `docker-compose.yml` - Production environment
- `docker-compose.dev.yml` - Development environment
- `.dockerignore` - Loại trừ files không cần thiết
- `.env.docker.example` - Template cho environment variables

## Cài đặt và Chạy

### 1. Production Environment

```bash
# Sao chép và cấu hình environment variables
cp .env.docker.example .env

# Chỉnh sửa .env với các giá trị thực tế
nano .env

# Build và chạy tất cả services
docker-compose up -d

# Xem logs
docker-compose logs -f backend

# Dừng services
docker-compose down
```

### 2. Development Environment

```bash
# Sao chép và cấu hình environment variables cho dev
cp .env.docker.example .env.dev

# Chạy development environment
docker-compose -f docker-compose.dev.yml up -d

# Xem logs với hot reload
docker-compose -f docker-compose.dev.yml logs -f backend-dev

# Dừng dev environment
docker-compose -f docker-compose.dev.yml down
```

### 3. Chạy với PgAdmin (Optional)

```bash
# Chạy với PgAdmin để quản lý database
docker-compose --profile tools up -d

# Truy cập PgAdmin tại: http://localhost:5050
# Email: <EMAIL> (hoặc theo .env)
# Password: admin123 (hoặc theo .env)
```

## Các Services

### Backend Application
- **Port**: 3000 (production), 3001 (development)
- **Health check**: Tự động kiểm tra tình trạng
- **Volumes**: Upload folder được mount

### PostgreSQL Database
- **Port**: 5432 (production), 5433 (development)
- **Database**: ai_erp (hoặc theo .env)
- **Persistent storage**: Docker volumes

### Redis Cache
- **Port**: 6379 (production), 6380 (development)
- **Persistent storage**: Docker volumes

### PgAdmin (Optional)
- **Port**: 5050 (production), 5051 (development)
- **Access**: http://localhost:5050

## Environment Variables

Các biến môi trường quan trọng:

```env
# Database
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=ai_erp
DB_USERNAME=postgres
DB_PASSWORD=your_password

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Email
MAIL_HOST=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# OpenAI
OPENAI_API_KEY=your_openai_key
```

## Debugging

### Development Mode
```bash
# Chạy với debug mode
docker-compose -f docker-compose.dev.yml up -d

# Attach debugger tại port 9229
# VS Code: Tạo launch.json với:
{
  "type": "node",
  "request": "attach",
  "name": "Docker Debug",
  "port": 9229,
  "address": "localhost",
  "localRoot": "${workspaceFolder}",
  "remoteRoot": "/app"
}
```

### Logs và Monitoring
```bash
# Xem logs của tất cả services
docker-compose logs -f

# Xem logs của service cụ thể
docker-compose logs -f backend

# Xem resource usage
docker stats

# Kiểm tra health status
docker-compose ps
```

## Troubleshooting

### 1. Port đã được sử dụng
```bash
# Kiểm tra ports đang sử dụng
netstat -tulpn | grep :3000

# Thay đổi port trong .env
PORT=3001
```

### 2. Database connection issues
```bash
# Kiểm tra PostgreSQL logs
docker-compose logs postgres

# Test connection
docker-compose exec postgres psql -U postgres -d ai_erp -c "SELECT 1;"
```

### 3. Build issues
```bash
# Clean build
docker-compose down
docker system prune -a
docker-compose build --no-cache
docker-compose up -d
```

### 4. Permission issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
```

## Production Deployment

### 1. Security Considerations
- Thay đổi tất cả default passwords
- Sử dụng secrets management
- Cấu hình firewall
- Enable SSL/TLS

### 2. Performance Optimization
- Sử dụng external database cho production
- Cấu hình Redis clustering
- Setup load balancer
- Monitor resource usage

### 3. Backup Strategy
```bash
# Database backup
docker-compose exec postgres pg_dump -U postgres ai_erp > backup.sql

# Restore database
docker-compose exec -T postgres psql -U postgres ai_erp < backup.sql
```

## Useful Commands

```bash
# Rebuild specific service
docker-compose build backend

# Scale services
docker-compose up -d --scale backend=3

# Execute commands in container
docker-compose exec backend npm run migration:run

# View container details
docker inspect redai-backend

# Clean up
docker-compose down -v  # Remove volumes
docker system prune -a  # Clean all unused resources
```
