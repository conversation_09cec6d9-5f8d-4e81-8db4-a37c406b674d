-- =====================================================
-- SIMPLE MIGRATION: Gộp Users vào Employees
-- <PERSON><PERSON> thành các bước đơn giản
-- Ngày: 2025-06-18
-- =====================================================

-- Backup dữ liệu
CREATE TABLE IF NOT EXISTS employees_backup_simple AS SELECT * FROM employees;
CREATE TABLE IF NOT EXISTS users_backup_simple AS SELECT * FROM users;

-- Thê<PERSON> các cột authentication vào employees
ALTER TABLE employees ADD COLUMN IF NOT EXISTS account_status VARCHAR(20) DEFAULT 'PENDING';
ALTER TABLE employees ADD COLUMN IF NOT EXISTS tenant_id INTEGER DEFAULT 1;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS employee_code VARCHAR(50);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS employee_name VARCHAR(255);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS position VARCHAR(255);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS department_id INTEGER;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS manager_id INTEGER;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS hire_date DATE;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS employment_type VARCHAR(50) DEFAULT 'FULL_TIME';
ALTER TABLE employees ADD COLUMN IF NOT EXISTS salary DECIMAL(15,2);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';
ALTER TABLE employees ADD COLUMN IF NOT EXISTS date_of_birth DATE;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS gender VARCHAR(10);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS marital_status VARCHAR(20);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS emergency_contact_name VARCHAR(255);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS emergency_contact_phone VARCHAR(20);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS bank_account_number VARCHAR(50);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS bank_name VARCHAR(255);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS social_insurance_number VARCHAR(50);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50);
ALTER TABLE employees ADD COLUMN IF NOT EXISTS notes TEXT;

-- Cập nhật dữ liệu từ cột cũ
UPDATE employees 
SET 
    employee_name = COALESCE(full_name, email),
    status = CASE 
        WHEN enable = true THEN 'active'
        ELSE 'inactive'
    END,
    account_status = CASE 
        WHEN enable = true THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    employee_code = 'REDAI' || LPAD(id::text, 3, '0')
WHERE employee_name IS NULL OR status IS NULL OR account_status IS NULL;

-- Cập nhật employees có email trùng với users
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.is_active = true THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END,
    date_of_birth = u.date_of_birth,
    gender = CASE 
        WHEN u.gender::text = 'male' THEN 'male'
        WHEN u.gender::text = 'female' THEN 'female'
        ELSE 'other'
    END,
    bank_account_number = u.account_number,
    tax_id = u.tax_code,
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Tạo employees mới cho users không có employee
INSERT INTO employees (
    email, password, account_status, employee_code, employee_name, status, tenant_id,
    date_of_birth, gender, bank_account_number, tax_id, employment_type, hire_date,
    created_at, updated_at
)
SELECT 
    u.email,
    u.password,
    CASE WHEN u.is_active = true THEN 'ACTIVE' ELSE 'INACTIVE' END,
    'REDAI' || LPAD((
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees WHERE employee_code ~ '^REDAI[0-9]+$'), 0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    )::text, 3, '0'),
    COALESCE(u.full_name, u.email),
    CASE WHEN u.is_active = true THEN 'active' ELSE 'inactive' END,
    1,
    u.date_of_birth,
    CASE 
        WHEN u.gender::text = 'male' THEN 'male'
        WHEN u.gender::text = 'female' THEN 'female'
        ELSE 'other'
    END,
    u.account_number,
    u.tax_code,
    'FULL_TIME',
    CURRENT_DATE,
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e WHERE LOWER(e.email) = LOWER(u.email)
);

-- Tạo mapping table
CREATE TEMP TABLE user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Cập nhật foreign key references
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ur.user_id = m.user_id;

UPDATE business_info bi
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE bi.user_id = m.user_id;

UPDATE two_factor_auth tfa
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE tfa.user_id = m.user_id;

UPDATE device_info di
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE di.user_id = m.user_id;

UPDATE auth_verification_logs avl
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE avl.user_id = m.user_id;

UPDATE point_purchase_transactions ppt
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ppt.user_id = m.user_id;

UPDATE affiliate_accounts aa
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE aa.user_id = m.user_id;

UPDATE affiliate_contracts ac
SET user_id = m.employee_id
FROM user_employee_mapping m
WHERE ac.user_id = m.user_id;

-- Tạo indexes
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_email ON employees(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_employees_account_status ON employees(account_status);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX IF NOT EXISTS idx_employees_employee_code ON employees(employee_code);

-- Kiểm tra kết quả
SELECT 
    'MIGRATION COMPLETED' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees;

SELECT 'SIMPLE MIGRATION COMPLETED SUCCESSFULLY' as final_status;
