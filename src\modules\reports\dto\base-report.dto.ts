import { IsEnum, IsOptional, IsString, IsBoolean, IsArray, IsDateString, ValidateNested, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReportType, ReportFormat } from '../enums';

/**
 * DTO cho khoảng thời gian
 */
export class DateRangeDto {
  @ApiProperty({
    description: '<PERSON><PERSON>y bắt đầu (ISO format)',
    example: '2024-01-01'
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc (ISO format)',
    example: '2024-12-31'
  })
  @IsDateString()
  endDate: string;
}

/**
 * DTO cho các bộ lọc báo cáo
 */
export class ReportFiltersDto {
  @ApiPropertyOptional({
    description: 'Danh sách ID phòng ban',
    type: [Number],
    example: [1, 2, 3]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  departmentIds?: number[];

  @ApiPropertyOptional({
    description: 'Danh sách ID nhân viên',
    type: [Number],
    example: [1, 2, 3]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  employeeIds?: number[];

  @ApiPropertyOptional({
    description: 'Danh sách ID dự án',
    type: [Number],
    example: [1, 2, 3]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  projectIds?: number[];

  @ApiPropertyOptional({
    description: 'Danh sách ID mục tiêu OKR',
    type: [Number],
    example: [1, 2, 3]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  objectiveIds?: number[];

  @ApiPropertyOptional({
    description: 'Danh sách trạng thái cần lọc',
    type: [String],
    example: ['active', 'completed']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  status?: string[];

  @ApiPropertyOptional({
    description: 'Các bộ lọc tùy chỉnh khác',
    example: { position: 'manager', employmentType: 'FULL_TIME' }
  })
  @IsOptional()
  customFilters?: Record<string, any>;
}

/**
 * DTO cho tùy chọn báo cáo
 */
export class ReportOptionsDto {
  @ApiPropertyOptional({
    description: 'Có bao gồm biểu đồ không',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeCharts?: boolean = true;

  @ApiPropertyOptional({
    description: 'Có bao gồm thống kê không',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeStatistics?: boolean = true;

  @ApiPropertyOptional({
    description: 'Có bao gồm chi tiết không',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeDetails?: boolean = true;

  @ApiProperty({
    enum: ReportFormat,
    description: 'Định dạng file xuất',
    default: ReportFormat.XLSX
  })
  @IsEnum(ReportFormat)
  format: ReportFormat = ReportFormat.XLSX;

  @ApiPropertyOptional({
    description: 'Ngôn ngữ báo cáo',
    enum: ['vi', 'en'],
    default: 'vi'
  })
  @IsOptional()
  @IsString()
  language?: 'vi' | 'en' = 'vi';

  @ApiPropertyOptional({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh'
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({
    description: 'Nhóm dữ liệu theo các trường',
    type: [String],
    example: ['department', 'position']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  groupBy?: string[];

  @ApiPropertyOptional({
    description: 'Sắp xếp dữ liệu',
    type: () => [SortOptionDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortOptionDto)
  sortBy?: SortOptionDto[];
}

/**
 * DTO cho tùy chọn sắp xếp
 */
export class SortOptionDto {
  @ApiProperty({
    description: 'Tên trường cần sắp xếp',
    example: 'employeeName'
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: ['asc', 'desc'],
    example: 'asc'
  })
  @IsEnum(['asc', 'desc'])
  direction: 'asc' | 'desc';
}

/**
 * Base DTO cho tất cả các request tạo báo cáo
 */
export class BaseReportRequestDto {
  @ApiProperty({
    enum: ReportType,
    description: 'Loại báo cáo cần tạo'
  })
  @IsEnum(ReportType)
  reportType: ReportType;

  @ApiProperty({
    description: 'Khoảng thời gian báo cáo',
    type: () => DateRangeDto
  })
  @ValidateNested()
  @Type(() => DateRangeDto)
  dateRange: DateRangeDto;

  @ApiPropertyOptional({
    description: 'Các bộ lọc cho báo cáo',
    type: () => ReportFiltersDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ReportFiltersDto)
  filters?: ReportFiltersDto;

  @ApiProperty({
    description: 'Tùy chọn cho báo cáo',
    type: () => ReportOptionsDto
  })
  @ValidateNested()
  @Type(() => ReportOptionsDto)
  options: ReportOptionsDto;
}

/**
 * DTO cho dữ liệu báo cáo trong response
 */
export class ReportDataDto {
  @ApiProperty({
    description: 'Tên file báo cáo',
    example: 'Bao_Cao_Nhan_Su_20241201_143022.xlsx'
  })
  fileName: string;

  @ApiProperty({
    description: 'URL download file',
    example: 'https://s3.amazonaws.com/bucket/reports/file.xlsx?expires=...'
  })
  downloadUrl: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000
  })
  fileSize: number;

  @ApiProperty({
    description: 'Thời gian tạo báo cáo',
    example: '2024-12-01T14:30:22.000Z'
  })
  generatedAt: string;

  @ApiProperty({
    description: 'Thời gian hết hạn download',
    example: '2024-12-02T14:30:22.000Z'
  })
  expiresAt: string;
}

/**
 * Base DTO cho response báo cáo
 */
export class BaseReportResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Dữ liệu báo cáo',
    type: () => ReportDataDto
  })
  data: ReportDataDto;

  @ApiPropertyOptional({
    description: 'Thông báo',
    example: 'Báo cáo được tạo thành công'
  })
  message?: string;

  @ApiPropertyOptional({
    description: 'Thông báo lỗi nếu có',
    example: 'Không thể tạo báo cáo'
  })
  error?: string;
}
