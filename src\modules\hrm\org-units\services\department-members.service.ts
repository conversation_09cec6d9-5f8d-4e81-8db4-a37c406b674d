import { Injectable, Logger } from '@nestjs/common';
import { DepartmentRepository } from '../repositories/department.repository';
import { EmployeeRepository } from '@/modules/hrm/employees/repositories/employee.repository';
import {
  DepartmentMemberDto,
  DepartmentMembersResponseDto,
} from '../dto/department/department-members.dto';
import { AppException } from '@/common';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * Service quản lý thành viên phòng ban
 * UPDATED: Sử dụng Employee entity thay vì User entity
 */
@Injectable()
export class DepartmentMembersService {
  private readonly logger = new Logger(DepartmentMembersService.name);

  constructor(
    private readonly departmentRepository: DepartmentRepository,
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách thành viên thuộc một phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @param departmentId ID phòng ban
   * @returns Thông tin phòng ban và danh sách thành viên
   */
  async getDepartmentMembers(
    tenantId: number,
    departmentId: number,
  ): Promise<DepartmentMembersResponseDto> {
    // Kiểm tra phòng ban tồn tại
    const department = await this.departmentRepository.findById(
      tenantId,
      departmentId,
    );
    if (!department) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${departmentId}`,
      );
    }

    // Lấy danh sách thành viên thuộc phòng ban từ Employee
    const members = await this.employeeRepository.findByDepartmentId(
      tenantId,
      departmentId,
    );

    // Chuyển đổi dữ liệu sang DTO
    const memberDtos: DepartmentMemberDto[] = members.map((member) => {
      const memberDto = new DepartmentMemberDto();
      memberDto.id = member.id;
      memberDto.fullName = member.employeeName; // Sử dụng employeeName từ Employee entity
      memberDto.email = member.email;
      memberDto.position = member.jobTitle; // Sử dụng jobTitle từ Employee entity
      memberDto.phoneNumber = member.emergencyContactPhone; // Sử dụng emergencyContactPhone từ Employee entity
      memberDto.avatarUrl = null; // Employee entity chưa có avatarUrl field
      memberDto.status = member.getUserStatus(); // Sử dụng helper method để convert sang UserStatus
      // Đánh dấu ai là trưởng phòng
      memberDto.isManager = department.managerId === member.id;

      return memberDto;
    });

    // Tạo response
    const response = new DepartmentMembersResponseDto();
    response.department = {
      id: department.id,
      name: department.name,
      managerId: department.managerId,
    };
    response.members = memberDtos;
    response.totalMembers = memberDtos.length;

    return response;
  }
}
