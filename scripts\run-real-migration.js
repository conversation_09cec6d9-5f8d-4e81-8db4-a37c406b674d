const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Script chạy real migration dựa trên cấu trúc database thực tế
 */

async function runRealMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for real migration');

    // Đọc real migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'real-migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting real database migration...');
    console.log('📄 Running real-migration.sql');

    // Chạy migration
    const result = await client.query(sqlContent);

    console.log('✅ Migration completed successfully!');

    // Verification
    console.log('\n📊 Running verification checks...');

    // Kiểm tra dữ liệu
    const dataCheck = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id,
        COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email
      FROM employees
    `);

    console.log('\n📈 Data verification:');
    console.table(dataCheck.rows);

    // Kiểm tra sample data với authentication
    const authSampleData = await client.query(`
      SELECT 
        id,
        email,
        employee_name,
        account_status,
        tenant_id,
        employee_code,
        status,
        CASE WHEN password IS NOT NULL THEN 'HAS_PASSWORD' ELSE 'NO_PASSWORD' END as password_status
      FROM employees 
      WHERE email IS NOT NULL
      ORDER BY id
      LIMIT 5
    `);

    console.log('\n📋 Sample employee authentication data:');
    console.table(authSampleData.rows);

    // Kiểm tra users table vẫn tồn tại
    try {
      const usersCheck = await client.query('SELECT COUNT(*) as count FROM users LIMIT 1');
      console.log(`\n👤 Users table: ${usersCheck.rows[0].count} records (still exists)`);
    } catch (error) {
      console.log('\n👤 Users table: Not accessible');
    }

    // Kiểm tra foreign key updates
    const foreignKeyCheck = await client.query(`
      SELECT 
        'user_roles' as table_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN user_id IN (SELECT id FROM employees) THEN 1 END) as valid_employee_refs
      FROM user_roles
      
      UNION ALL
      
      SELECT 
        'employees_with_auth' as table_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN password IS NOT NULL AND email IS NOT NULL THEN 1 END) as ready_for_auth
      FROM employees
    `);

    console.log('\n🔗 Foreign key and authentication readiness:');
    console.table(foreignKeyCheck.rows);

    await client.end();
    console.log('\n✅ Real migration completed successfully!');

    // Tóm tắt kết quả
    console.log(`
🎯 REAL MIGRATION SUMMARY:
- ✅ Migrated user authentication data to employees table
- ✅ Created new employees for users without existing employee records
- ✅ Updated foreign key references to point to employees
- ✅ Added necessary constraints and indexes
- ✅ Preserved all existing employee data
- ⚠️ Users table still exists (not removed in this migration)

🚀 NEXT STEPS:
1. Test authentication with EmployeeAuthService
2. Verify login works with existing user credentials
3. Test all API endpoints that use authentication
4. If everything works, can run cleanup to remove users table

🔄 BACKUP TABLES CREATED:
- employees_backup_real (original employees data)
- users_backup_real (original users data)

📝 AUTHENTICATION READY:
- Employees now have email, password, and account_status
- Foreign key references updated to use employee IDs
- System ready to use EmployeeAuthService instead of UserAuthService
    `);

  } catch (error) {
    console.error('\n❌ Real migration failed:', error.message);
    console.error('Full error:', error);
    
    console.log(`
🔄 ROLLBACK OPTIONS:
1. Restore from backup tables:
   - DROP TABLE employees; 
   - ALTER TABLE employees_backup_real RENAME TO employees;
2. Check backup tables: employees_backup_real, users_backup_real
    `);
    
    process.exit(1);
  }
}

// Chạy migration nếu script được gọi trực tiếp
if (require.main === module) {
  console.log('🎯 Starting real User-Employee merge migration...');
  runRealMigration();
}

module.exports = { runRealMigration };
