# =================================
# DOCKER ENVIRONMENT CONFIGURATION
# =================================

# Application
NODE_ENV=production
PORT=3000

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=ai_erp
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password_here

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters
JWT_EXPIRES_IN=7d

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password_here
MAIL_DEFAULT_FROM="RedAI <<EMAIL>>"

# AWS S3 Configuration (if using)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-southeast-1
AWS_S3_BUCKET=your-s3-bucket-name

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud Configuration (if using)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
GOOGLE_CLOUD_PROJECT_ID=your-project-id

# PgAdmin Configuration (Optional)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=5050

# Logging
LOG_LEVEL=info
ENABLE_TENANT_DEBUG=false

# Security
CORS_ORIGIN=http://localhost:3001,http://localhost:3000
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload
MAX_FILE_SIZE=********
UPLOAD_DEST=./uploads

# Queue Configuration
BULL_REDIS_HOST=redis
BULL_REDIS_PORT=6379
BULL_REDIS_DB=1
