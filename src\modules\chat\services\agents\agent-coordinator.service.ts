import { Injectable, Logger } from '@nestjs/common';
import {
  BaseAgent,
  AgentConfig,
  AgentResponse,
  UserIntent,
  IntentCategory,
  RoutingResult,
} from './base-agent.interface';

/**
 * Agent Coordinator - <PERSON><PERSON><PERSON><PERSON> phối trung tâm cho hệ thống multi-agent
 * Phân tích intent và routing request đến agent phù hợp
 */
@Injectable()
export class AgentCoordinatorService {
  private readonly logger = new Logger(AgentCoordinatorService.name);
  private agents: Map<string, BaseAgent> = new Map();

  /**
   * Đăng ký agent vào hệ thống
   */
  registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.name, agent);
    this.logger.log(`Đã đăng ký agent: ${agent.name}`);
  }

  /**
   * Hủy đăng ký agent
   */
  unregisterAgent(agentName: string): void {
    this.agents.delete(agentName);
    this.logger.log(`Đã hủy đăng ký agent: ${agentName}`);
  }

  /**
   * <PERSON><PERSON>y danh sách tất cả agents
   */
  getAgents(): BaseAgent[] {
    return Array.from(this.agents.values());
  }

  /**
   * <PERSON>ân tích intent từ tin nhắn user
   */
  async analyzeIntent(message: string, context?: any): Promise<UserIntent> {
    const normalizedMessage = message.toLowerCase().trim();

    // Keywords mapping cho từng category
    const categoryKeywords = {
      [IntentCategory.HRM]: [
        'nhân viên', 'employee', 'user', 'người dùng', 'phòng ban', 'department',
        'chấm công', 'attendance', 'lương', 'salary', 'hợp đồng', 'contract',
        'tạo user', 'xóa user', 'cập nhật email', 'quản lý nhân sự'
      ],
      [IntentCategory.PROJECT]: [
        'dự án', 'project', 'todo', 'công việc', 'task', 'gantt',
        'tiến độ', 'progress', 'deadline', 'milestone', 'collaborator'
      ],
      [IntentCategory.COMMUNICATION]: [
        'email', 'gửi mail', 'thông báo', 'notification', 'tin nhắn', 'message'
      ],
      [IntentCategory.ANALYTICS]: [
        'thống kê', 'statistics', 'báo cáo', 'report', 'phân tích', 'analytics',
        'dashboard', 'biểu đồ', 'chart', 'số liệu', 'excel', 'xuất', 'export',
        'tải về', 'download', 'file', 'bảng tính', 'spreadsheet'
      ],
      [IntentCategory.DOCUMENT]: [
        'tài liệu', 'document', 'file', 'upload', 'download', 'chia sẻ', 'share'
      ],
    };

    // Tính điểm cho từng category
    const scores: Record<IntentCategory, number> = {
      [IntentCategory.HRM]: 0,
      [IntentCategory.PROJECT]: 0,
      [IntentCategory.COMMUNICATION]: 0,
      [IntentCategory.ANALYTICS]: 0,
      [IntentCategory.DOCUMENT]: 0,
      [IntentCategory.GENERAL]: 0,
      [IntentCategory.UNKNOWN]: 0,
    };

    // Đếm keywords match
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      for (const keyword of keywords) {
        if (normalizedMessage.includes(keyword)) {
          scores[category as IntentCategory] += 1;
        }
      }
    }

    // Tìm category có điểm cao nhất
    const maxScore = Math.max(...Object.values(scores));
    const bestCategory = Object.entries(scores).find(
      ([_, score]) => score === maxScore
    )?.[0] as IntentCategory;

    // Trích xuất action cụ thể
    let action = 'general';
    if (normalizedMessage.includes('tạo') || normalizedMessage.includes('create')) {
      action = 'create';
    } else if (normalizedMessage.includes('xóa') || normalizedMessage.includes('delete')) {
      action = 'delete';
    } else if (normalizedMessage.includes('cập nhật') || normalizedMessage.includes('update')) {
      action = 'update';
    } else if (normalizedMessage.includes('lấy') || normalizedMessage.includes('get') || normalizedMessage.includes('xem')) {
      action = 'get';
    } else if (normalizedMessage.includes('tìm') || normalizedMessage.includes('search')) {
      action = 'search';
    }

    // Trích xuất entities (đơn giản)
    const entities: Record<string, any> = {};
    
    // Tìm ID numbers
    const idMatches = normalizedMessage.match(/\b(\d+)\b/g);
    if (idMatches) {
      entities.ids = idMatches.map(id => parseInt(id));
    }

    // Tìm email
    const emailMatch = normalizedMessage.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
    if (emailMatch) {
      entities.email = emailMatch[0];
    }

    const confidence = maxScore > 0 ? Math.min(maxScore / 3, 1) : 0.1;

    return {
      category: bestCategory || IntentCategory.UNKNOWN,
      action,
      entities,
      confidence,
      context,
    };
  }

  /**
   * Route request đến agent phù hợp
   */
  async routeToAgent(intent: UserIntent, message: string, context?: any): Promise<RoutingResult> {
    const candidates: Array<{ agent: BaseAgent; score: number }> = [];

    // Tính điểm cho từng agent
    for (const agent of this.agents.values()) {
      const score = await agent.canHandle(message, { intent, ...context });
      if (score > 0) {
        candidates.push({ agent, score });
      }
    }

    // Sắp xếp theo điểm và priority
    candidates.sort((a, b) => {
      const scoreA = a.score * a.agent.priority;
      const scoreB = b.score * b.agent.priority;
      return scoreB - scoreA;
    });

    if (candidates.length === 0) {
      return {
        selectedAgent: 'general',
        confidence: 0.1,
        reason: 'Không tìm thấy agent phù hợp',
        fallbackAgents: [],
      };
    }

    const best = candidates[0];
    const fallbacks = candidates.slice(1, 3).map(c => c.agent.name);

    return {
      selectedAgent: best.agent.name,
      confidence: best.score,
      reason: `Agent ${best.agent.name} có điểm cao nhất: ${best.score}`,
      fallbackAgents: fallbacks,
    };
  }

  /**
   * Xử lý request với multi-agent coordination
   */
  async processRequest(
    message: string,
    config: AgentConfig,
    context?: any,
  ): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      // 1. Phân tích intent
      const intent = await this.analyzeIntent(message, context);
      this.logger.debug(`Intent phân tích: ${JSON.stringify(intent)}`);

      // 2. Route đến agent
      const routing = await this.routeToAgent(intent, message, context);
      this.logger.debug(`Routing kết quả: ${JSON.stringify(routing)}`);

      // 3. Lấy agent được chọn
      const selectedAgent = this.agents.get(routing.selectedAgent);
      if (!selectedAgent) {
        throw new Error(`Agent ${routing.selectedAgent} không tồn tại`);
      }

      // 4. Xử lý với agent được chọn
      const response = await selectedAgent.process(message, { intent, ...context }, config);

      // 5. Thêm metadata
      response.metadata = {
        ...response.metadata,
        agentUsed: selectedAgent.name,
        intentCategory: intent.category,
        intentAction: intent.action,
        routingConfidence: routing.confidence,
        totalExecutionTime: Date.now() - startTime,
      };

      // 6. Kiểm tra nếu cần handoff
      if (response.needsHandoff && response.suggestedAgent) {
        const handoffAgent = this.agents.get(response.suggestedAgent);
        if (handoffAgent) {
          this.logger.log(`Handoff từ ${selectedAgent.name} đến ${response.suggestedAgent}`);
          const handoffResponse = await handoffAgent.process(message, context, config);
          handoffResponse.metadata = {
            ...handoffResponse.metadata,
            handoffFrom: selectedAgent.name,
            originalAgent: selectedAgent.name,
          };
          return handoffResponse;
        }
      }

      return response;
    } catch (error) {
      this.logger.error(`Lỗi trong coordinator: ${error.message}`, error.stack);
      return {
        result: `Xin lỗi, đã có lỗi xảy ra khi xử lý yêu cầu: ${error.message}`,
        confidence: 0,
        error: error.message,
        metadata: {
          executionTime: Date.now() - startTime,
          error: true,
        },
      };
    }
  }

  /**
   * Lấy thống kê về các agents
   */
  getAgentStats(): any {
    const stats = {
      totalAgents: this.agents.size,
      agents: Array.from(this.agents.values()).map(agent => ({
        name: agent.name,
        description: agent.description,
        priority: agent.priority,
        toolsCount: agent.getTools().length,
      })),
    };

    return stats;
  }
}
