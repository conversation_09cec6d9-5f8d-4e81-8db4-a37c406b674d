-- =====================================================
-- MIGRATION 002: Migrate dữ liệu từ USERS sang EMPLOYEES
-- Mụ<PERSON> tiêu: Gộp authentication data từ Users vào Employees
-- Ngày: 2025-06-18
-- =====================================================

-- =====================================================
-- PHASE 1: BACKUP VÀ CHUẨN BỊ
-- =====================================================

-- Backup bảng users
CREATE TABLE users_backup AS SELECT * FROM users;

-- Tạo bảng mapping để track việc migration
CREATE TABLE user_employee_migration_log (
    id SERIAL PRIMARY KEY,
    old_user_id INTEGER,
    employee_id INTEGER,
    user_email VARCHAR(255),
    employee_email VARCHAR(255),
    employee_name VARCHAR(255),
    migration_method VARCHAR(50), -- 'update_existing', 'create_new', 'skip'
    migration_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'success', 'failed'
    error_message TEXT,
    created_at BIGINT DEFAULT EXTRACT(epoch FROM now()) * 1000
);

-- =====================================================
-- PHASE 2: PHÂN TÍCH VÀ TẠO MAPPING
-- =====================================================

-- Tìm employees có email trùng với users (sẽ update authentication data)
INSERT INTO user_employee_migration_log (
    old_user_id,
    employee_id,
    user_email,
    employee_email,
    employee_name,
    migration_method
)
SELECT
    u.id as old_user_id,
    e.id as employee_id,
    u.email as user_email,
    e.email as employee_email,
    e.employee_name,
    'update_existing' as migration_method
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- =====================================================
-- PHASE 3: CẬP NHẬT AUTHENTICATION DATA CHO EMPLOYEES CÓ SẴN
-- =====================================================

-- Cập nhật password và account status cho employees có user tương ứng
UPDATE employees e
SET 
    password = u.password,
    account_status = CASE 
        WHEN u.is_active = true THEN 'ACTIVE'
        WHEN u.is_active = false THEN 'INACTIVE'
        ELSE 'PENDING'
    END,
    -- Cập nhật thông tin cá nhân từ users
    date_of_birth = u.date_of_birth,
    gender = CASE 
        WHEN u.gender = 'male' THEN 'male'
        WHEN u.gender = 'female' THEN 'female'
        ELSE 'other'
    END,
    bank_account_number = u.account_number,
    bank_name = (SELECT bank_name FROM banks WHERE bank_code = u.bank_code),
    tax_id = u.tax_code,
    -- Cập nhật tenant_id từ user (nếu có)
    tenant_id = COALESCE(u.tenant_id, 1), -- Default tenant_id = 1 nếu null
    updated_at = EXTRACT(epoch FROM now()) * 1000
FROM users u
WHERE LOWER(e.email) = LOWER(u.email);

-- Cập nhật migration status cho các record đã update thành công
UPDATE user_employee_migration_log 
SET migration_status = 'success'
WHERE migration_method = 'update_existing'
AND employee_id IN (
    SELECT e.id FROM employees e
    INNER JOIN users u ON LOWER(e.email) = LOWER(u.email)
);

-- =====================================================
-- PHASE 4: TẠO EMPLOYEES MỚI CHO USERS KHÔNG CÓ EMPLOYEE
-- =====================================================

-- Tạo employees mới cho users không có employee tương ứng
INSERT INTO employees (
    email,
    password,
    account_status,
    employee_code,
    employee_name,
    status,
    tenant_id,
    date_of_birth,
    gender,
    bank_account_number,
    tax_id,
    employment_type,
    hire_date,
    created_at,
    updated_at
)
SELECT 
    u.email,
    u.password,
    CASE 
        WHEN u.is_active = true THEN 'ACTIVE'
        WHEN u.is_active = false THEN 'INACTIVE'
        ELSE 'PENDING'
    END as account_status,
    'REDAI' || (
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_code FROM 6) AS INTEGER)) 
             FROM employees 
             WHERE employee_code ~ '^REDAI[0-9]+$'), 
            0
        ) + ROW_NUMBER() OVER (ORDER BY u.id)
    ) as employee_code,
    COALESCE(u.full_name, u.email) as employee_name,
    CASE 
        WHEN u.is_active = true THEN 'active'
        ELSE 'inactive'
    END as status,
    COALESCE(u.tenant_id, 1) as tenant_id, -- Default tenant_id = 1
    u.date_of_birth,
    CASE 
        WHEN u.gender = 'male' THEN 'male'
        WHEN u.gender = 'female' THEN 'female'
        ELSE 'other'
    END as gender,
    u.account_number as bank_account_number,
    u.tax_code as tax_id,
    'FULL_TIME' as employment_type, -- Default employment type
    CURRENT_DATE as hire_date, -- Default hire date
    u.created_at,
    EXTRACT(epoch FROM now()) * 1000 as updated_at
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM employees e 
    WHERE LOWER(e.email) = LOWER(u.email)
);

-- Cập nhật mapping cho users được tạo employee mới
INSERT INTO user_employee_migration_log (
    old_user_id,
    employee_id,
    user_email,
    employee_email,
    employee_name,
    migration_method,
    migration_status
)
SELECT
    u.id as old_user_id,
    e.id as employee_id,
    u.email as user_email,
    e.email as employee_email,
    e.employee_name,
    'create_new' as migration_method,
    'success' as migration_status
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email)
WHERE NOT EXISTS (
    SELECT 1 FROM user_employee_migration_log m 
    WHERE m.old_user_id = u.id
);

-- =====================================================
-- PHASE 5: CẬP NHẬT FOREIGN KEY REFERENCES
-- =====================================================

-- Cập nhật user_roles.user_id -> employee_id
UPDATE user_roles ur
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE ur.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật user_has_permissions.user_id -> employee_id (nếu bảng tồn tại)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_has_permissions') THEN
        UPDATE user_has_permissions uhp
        SET user_id = m.employee_id
        FROM user_employee_migration_log m
        WHERE uhp.user_id = m.old_user_id
        AND m.migration_status = 'success';
    END IF;
END $$;

-- Cập nhật business_info.user_id -> employee_id
UPDATE business_info bi
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE bi.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật two_factor_auth.user_id -> employee_id
UPDATE two_factor_auth tfa
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE tfa.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật device_info.user_id -> employee_id
UPDATE device_info di
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE di.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật auth_verification_logs.user_id -> employee_id
UPDATE auth_verification_logs avl
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE avl.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật point_purchase_transactions.user_id -> employee_id
UPDATE point_purchase_transactions ppt
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE ppt.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật affiliate_accounts.user_id -> employee_id
UPDATE affiliate_accounts aa
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE aa.user_id = m.old_user_id
AND m.migration_status = 'success';

-- Cập nhật affiliate_contracts.user_id -> employee_id
UPDATE affiliate_contracts ac
SET user_id = m.employee_id
FROM user_employee_migration_log m
WHERE ac.user_id = m.old_user_id
AND m.migration_status = 'success';

-- =====================================================
-- PHASE 6: VERIFICATION VÀ CLEANUP
-- =====================================================

-- Kiểm tra kết quả migration
SELECT
    migration_method,
    migration_status,
    COUNT(*) as count
FROM user_employee_migration_log
GROUP BY migration_method, migration_status
ORDER BY migration_method, migration_status;

-- Kiểm tra employees có authentication data
SELECT
    COUNT(*) as total_employees,
    COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
    COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
    COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id
FROM employees;

-- Kiểm tra foreign key references đã được cập nhật
SELECT 
    'user_roles' as table_name,
    COUNT(*) as records_with_employee_id
FROM user_roles ur
INNER JOIN employees e ON ur.user_id = e.id

UNION ALL

SELECT 
    'business_info' as table_name,
    COUNT(*) as records_with_employee_id
FROM business_info bi
INNER JOIN employees e ON bi.user_id = e.id

UNION ALL

SELECT 
    'two_factor_auth' as table_name,
    COUNT(*) as records_with_employee_id
FROM two_factor_auth tfa
INNER JOIN employees e ON tfa.user_id = e.id;

-- Tạo summary report
SELECT 
    'MIGRATION SUMMARY' as report_type,
    (SELECT COUNT(*) FROM users_backup) as original_users_count,
    (SELECT COUNT(*) FROM employees) as total_employees_after_migration,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'success') as successful_migrations,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'failed') as failed_migrations;

COMMIT;
