#!/bin/bash

# =====================================================
# Script thực hiện tất cả các bước migration User-Employee
# =====================================================

set -e  # Exit on any error

echo "🚀 Starting User-Employee Migration Process..."
echo "================================================"

# Load environment variables
if [ -f .env.development ]; then
    export $(cat .env.development | grep -v '^#' | xargs)
    echo "✅ Loaded .env.development"
elif [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo "✅ Loaded .env"
else
    echo "❌ No .env file found!"
    exit 1
fi

# Display database configuration
echo ""
echo "📋 Database Configuration:"
echo "  Host: ${DB_HOST:-localhost}"
echo "  Port: ${DB_PORT:-5432}"
echo "  Database: ${DB_DATABASE:-redai_db}"
echo "  Username: ${DB_USERNAME:-postgres}"
echo "  SSL: ${DB_SSL:-false}"
echo ""

# Confirm before proceeding
read -p "🔍 Do you want to proceed with migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Migration cancelled by user"
    exit 1
fi

# =====================================================
# Step 1: Test Database Connection
# =====================================================

echo "🔗 Step 1: Testing database connection..."
if command -v node &> /dev/null; then
    if [ -f scripts/test-db-connection.js ]; then
        node scripts/test-db-connection.js
        if [ $? -ne 0 ]; then
            echo "❌ Database connection failed!"
            exit 1
        fi
    else
        echo "⚠️  test-db-connection.js not found, skipping connection test"
    fi
else
    echo "⚠️  Node.js not found, skipping connection test"
fi

# =====================================================
# Step 2: Create Database Backup
# =====================================================

echo ""
echo "💾 Step 2: Creating database backup..."

BACKUP_FILE="backup/db_backup_before_user_employee_merge_$(date +%Y%m%d_%H%M%S).sql"
mkdir -p backup

if command -v pg_dump &> /dev/null; then
    echo "📦 Creating backup: $BACKUP_FILE"
    
    # Build pg_dump command
    PG_DUMP_CMD="pg_dump"
    
    if [ ! -z "$DB_HOST" ]; then
        PG_DUMP_CMD="$PG_DUMP_CMD -h $DB_HOST"
    fi
    
    if [ ! -z "$DB_PORT" ]; then
        PG_DUMP_CMD="$PG_DUMP_CMD -p $DB_PORT"
    fi
    
    if [ ! -z "$DB_USERNAME" ]; then
        PG_DUMP_CMD="$PG_DUMP_CMD -U $DB_USERNAME"
    fi
    
    if [ ! -z "$DB_DATABASE" ]; then
        PG_DUMP_CMD="$PG_DUMP_CMD -d $DB_DATABASE"
    fi
    
    # Set password if provided
    if [ ! -z "$DB_PASSWORD" ]; then
        export PGPASSWORD="$DB_PASSWORD"
    fi
    
    # Execute backup
    $PG_DUMP_CMD > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Backup created successfully: $BACKUP_FILE"
        echo "📊 Backup size: $(du -h "$BACKUP_FILE" | cut -f1)"
    else
        echo "❌ Backup failed!"
        exit 1
    fi
    
    # Clear password
    unset PGPASSWORD
else
    echo "⚠️  pg_dump not found, skipping backup"
    echo "   Please create manual backup before proceeding"
    read -p "   Continue without backup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Migration cancelled"
        exit 1
    fi
fi

# =====================================================
# Step 3: Run Migration Script
# =====================================================

echo ""
echo "🔄 Step 3: Running migration script..."

MIGRATION_FILE="database/migrations/final-user-employee-merge.sql"

if [ ! -f "$MIGRATION_FILE" ]; then
    echo "❌ Migration file not found: $MIGRATION_FILE"
    exit 1
fi

if command -v psql &> /dev/null; then
    echo "📝 Executing migration: $MIGRATION_FILE"
    
    # Build psql command
    PSQL_CMD="psql"
    
    if [ ! -z "$DB_HOST" ]; then
        PSQL_CMD="$PSQL_CMD -h $DB_HOST"
    fi
    
    if [ ! -z "$DB_PORT" ]; then
        PSQL_CMD="$PSQL_CMD -p $DB_PORT"
    fi
    
    if [ ! -z "$DB_USERNAME" ]; then
        PSQL_CMD="$PSQL_CMD -U $DB_USERNAME"
    fi
    
    if [ ! -z "$DB_DATABASE" ]; then
        PSQL_CMD="$PSQL_CMD -d $DB_DATABASE"
    fi
    
    # Set password if provided
    if [ ! -z "$DB_PASSWORD" ]; then
        export PGPASSWORD="$DB_PASSWORD"
    fi
    
    # Execute migration
    $PSQL_CMD -f "$MIGRATION_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Migration executed successfully!"
    else
        echo "❌ Migration failed!"
        echo "🔄 You can restore from backup: $BACKUP_FILE"
        exit 1
    fi
    
    # Clear password
    unset PGPASSWORD
else
    echo "❌ psql not found!"
    echo "   Please install PostgreSQL client tools"
    exit 1
fi

# =====================================================
# Step 4: Run Verification Tests
# =====================================================

echo ""
echo "🧪 Step 4: Running verification tests..."

if [ -f scripts/test-user-employee-merge.js ]; then
    node scripts/test-user-employee-merge.js
    
    if [ $? -eq 0 ]; then
        echo "✅ All verification tests passed!"
    else
        echo "❌ Some verification tests failed!"
        echo "   Please check the output above"
        exit 1
    fi
else
    echo "⚠️  Test script not found: scripts/test-user-employee-merge.js"
fi

# =====================================================
# Step 5: Run Cleanup (Optional)
# =====================================================

echo ""
echo "🧹 Step 5: Running cleanup..."

if [ -f scripts/cleanup-user-entity.sh ]; then
    chmod +x scripts/cleanup-user-entity.sh
    ./scripts/cleanup-user-entity.sh
    
    if [ $? -eq 0 ]; then
        echo "✅ Cleanup completed successfully!"
    else
        echo "⚠️  Cleanup completed with warnings"
    fi
else
    echo "⚠️  Cleanup script not found: scripts/cleanup-user-entity.sh"
fi

# =====================================================
# Final Summary
# =====================================================

echo ""
echo "🎉 Migration Process Completed!"
echo "================================"
echo ""
echo "📋 Summary:"
echo "✅ Database backup created: $BACKUP_FILE"
echo "✅ Migration script executed successfully"
echo "✅ Verification tests passed"
echo "✅ Cleanup completed"
echo ""
echo "🚀 Next Steps:"
echo "1. Test your application authentication"
echo "2. Verify all APIs are working correctly"
echo "3. Monitor for any issues"
echo ""
echo "📖 For more details, see: docs/user-employee-merge-guide.md"
echo ""
echo "⚠️  If you encounter issues, you can restore from backup:"
echo "   psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE < $BACKUP_FILE"
