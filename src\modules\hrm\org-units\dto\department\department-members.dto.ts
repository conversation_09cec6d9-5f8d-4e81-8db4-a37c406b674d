import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * DTO đại diện cho thông tin thành viên trong phòng ban
 * UPDATED: Sử dụng Employee entity thay vì User entity
 */
export class DepartmentMemberDto {
  /**
   * ID của nhân viên
   */
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1,
  })
  id: number;

  /**
   * Tên đầy đủ của nhân viên
   */
  @ApiProperty({
    description: 'Họ tên đầy đủ của nhân viên',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  fullName: string | null;

  /**
   * Email của nhân viên
   */
  @ApiProperty({
    description: 'Email của nhân viên',
    example: 'nguy<PERSON><PERSON>@example.com',
  })
  email: string;

  /**
   * <PERSON><PERSON> trí công việc của nhân viên
   */
  @ApiProperty({
    description: '<PERSON><PERSON> trí công việc của nhân viên',
    example: '<PERSON>hà phát triển',
    nullable: true,
  })
  position: string | null;

  /**
   * Số điện thoại liên hệ khẩn cấp của nhân viên
   */
  @ApiProperty({
    description: 'Số điện thoại liên hệ khẩn cấp của nhân viên',
    example: '0987654321',
    nullable: true,
  })
  phoneNumber: string | null;

  /**
   * URL avatar của nhân viên
   */
  @ApiProperty({
    description: 'URL avatar của nhân viên',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatarUrl: string | null;

  /**
   * Trạng thái tài khoản của nhân viên
   */
  @ApiProperty({
    description: 'Trạng thái tài khoản của nhân viên',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    nullable: true,
  })
  status: UserStatus | null;

  /**
   * Nhân viên có phải là trưởng phòng không
   */
  @ApiProperty({
    description: 'Nhân viên có phải là trưởng phòng không',
    example: true,
  })
  isManager: boolean;
}

/**
 * DTO đại diện cho danh sách thành viên phòng ban và thông tin tổng hợp
 */
export class DepartmentMembersResponseDto {
  /**
   * Thông tin phòng ban
   */
  @ApiProperty({
    description: 'Thông tin phòng ban',
    example: {
      id: 1,
      name: 'Phòng Kỹ thuật',
    },
  })
  department: {
    id: number;
    name: string;
    managerId: number | null;
  };

  /**
   * Danh sách thành viên
   */
  @ApiProperty({
    description: 'Danh sách thành viên trong phòng ban',
    type: [DepartmentMemberDto],
  })
  members: DepartmentMemberDto[];

  /**
   * Tổng số thành viên trong phòng ban
   */
  @ApiProperty({
    description: 'Tổng số thành viên trong phòng ban',
    example: 5,
  })
  totalMembers: number;
}
