import { ReportType, ReportFormat } from '../enums';

/**
 * Interface chính cho hệ thống báo cáo
 */

export interface ReportRequest {
  reportType: ReportType;
  dateRange: DateRange;
  filters: ReportFilters;
  options: ReportOptions;
  tenantId: number;
  userId: number;
}

export interface ReportResponse {
  success: boolean;
  data: {
    fileName: string;
    downloadUrl: string;
    fileSize: number;
    generatedAt: string;
    expiresAt: string;
  };
  message?: string;
  error?: string;
}

export interface DateRange {
  startDate: string; // ISO date string
  endDate: string;   // ISO date string
}

export interface ReportFilters {
  departmentIds?: number[];
  employeeIds?: number[];
  projectIds?: number[];
  objectiveIds?: number[];
  status?: string[];
  customFilters?: Record<string, any>;
}

export interface ReportOptions {
  includeCharts: boolean;
  includeStatistics: boolean;
  includeDetails: boolean;
  format: ReportFormat;
  language: 'vi' | 'en';
  timezone?: string;
  groupBy?: string[];
  sortBy?: SortOption[];
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
}

// Note: ReportType and ReportFormat are now defined in enums
// export type ReportType =
//   | 'hr_employees'
//   | 'hr_statistics'
//   | 'attendance_details'
//   | 'attendance_summary'
//   | 'okr_progress'
//   | 'okr_statistics'
//   | 'project_progress'
//   | 'project_performance'
//   | 'todo_summary'
//   | 'custom';

// export type ReportFormat = 'xlsx' | 'csv' | 'pdf';

/**
 * Interface cho data được truyền vào báo cáo
 */
export interface ReportData {
  items: any[];
  summary?: ReportSummary;
  metadata: ReportMetadata;
}

export interface ReportSummary {
  totalItems: number;
  calculations: Record<string, number>;
  groupedData?: Record<string, any[]>;
  statistics?: Record<string, any>;
}

export interface ReportMetadata {
  generatedBy: string;
  generatedAt: Date;
  dataSource: string;
  version: string;
  filters: ReportFilters;
  dateRange: DateRange;
}

/**
 * Interface cho template báo cáo
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: ReportType;
  schema: any; // ExcelReportSchema
  isDefault: boolean;
  isActive: boolean;
  createdBy: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface cho audit log báo cáo
 */
export interface ReportAuditLog {
  id: number;
  reportType: ReportType;
  fileName: string;
  fileSize: number;
  generatedBy: number;
  tenantId: number;
  filters: ReportFilters;
  generatedAt: Date;
  downloadedAt?: Date;
  downloadCount: number;
  ipAddress: string;
  userAgent: string;
}
