const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function runFinalMigration() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database for final migration');

    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'final-migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting final database migration...');
    await client.query(sqlContent);
    console.log('✅ Migration completed successfully!');

    // Verification
    const dataCheck = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts,
        COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email
      FROM employees
    `);

    console.log('\n📈 Final verification:');
    console.table(dataCheck.rows);

    const authSample = await client.query(`
      SELECT 
        id, email, employee_name, account_status,
        CASE WHEN password IS NOT NULL THEN 'YES' ELSE 'NO' END as has_password
      FROM employees 
      WHERE email IS NOT NULL
      LIMIT 3
    `);

    console.log('\n📋 Authentication ready employees:');
    console.table(authSample.rows);

    await client.end();
    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log(`
🎯 SUMMARY:
- ✅ Users data migrated to employees
- ✅ Authentication fields populated
- ✅ Foreign keys updated
- ✅ System ready for EmployeeAuthService

🚀 NEXT: Test login with EmployeeAuthService!
    `);

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  runFinalMigration();
}

module.exports = { runFinalMigration };
