import { Injectable } from '@nestjs/common';
import { IntentCategory } from './base-agent.interface';
import { BaseReactAgent } from './base-react-agent';

// Import Analytics tool providers
import { StatisticsToolsProvider } from '../tools/statistics-tools.provider';
import { OkrToolsProvider } from '../tools/okr-tools.provider';
import { KeyResultSupportService } from '@modules/okrs/services';
import { ReportToolsProvider } from '@/modules/reports/services/report-tools.provider';

/**
 * Analytics Agent - Chuyên xử lý các yêu cầu liên quan đến thống kê và phân tích
 */
@Injectable()
export class AnalyticsAgentService extends BaseReactAgent {
  readonly name = 'analytics-agent';
  readonly description = 'Agent chuyên xử lý thống kê, báo cáo Excel, phân tích dữ liệu, OKR';
  readonly priority = 7;
  readonly keywords = [
    'thống kê', 'statistics', 'báo cáo', 'report', 'phân tích', 'analytics',
    'dashboard', 'biểu đồ', 'chart', 'số liệu', 'data', 'dữ liệu',
    'okr', 'objective', 'key result', 'mục tiêu', 'kết quả chính',
    'performance', 'hiệu suất', 'đánh giá', 'evaluation', 'kpi',
    'excel', 'xuất', 'export', 'download', 'file', 'tải về',
    'nhân viên', 'employee', 'chấm công', 'attendance', 'hr'
  ];

  constructor(
    private readonly statisticsToolsProvider: StatisticsToolsProvider,
    private readonly okrToolsProvider: OkrToolsProvider,
    private readonly keyResultSupportService: KeyResultSupportService,
    private readonly reportToolsProvider: ReportToolsProvider,
  ) {
    super();
  }

  /**
   * Kiểm tra xem có match với intent category không
   */
  protected matchesIntentCategory(category: string): boolean {
    return category === IntentCategory.ANALYTICS;
  }

  /**
   * Tính bonus score cho Analytics patterns
   */
  protected calculateBonusScore(message: string, baseScore: number): number {
    let bonus = 0;

    if (message.includes('thống kê') || message.includes('statistics')) {
      bonus += 0.2;
    }

    if (message.includes('báo cáo') || message.includes('report')) {
      bonus += 0.15;
    }

    if (message.includes('excel') || message.includes('xuất') || message.includes('export')) {
      bonus += 0.2;
    }

    if (message.includes('nhân viên') || message.includes('employee') || message.includes('chấm công')) {
      bonus += 0.1;
    }

    if (message.includes('okr') || message.includes('mục tiêu')) {
      bonus += 0.15;
    }

    return Math.min(baseScore + bonus, 1);
  }

  /**
   * Lấy tất cả tools của Analytics
   */
  getTools(): any[] {
    return [
      ...this.statisticsToolsProvider.getTools(),
      ...this.okrToolsProvider.getTools(),
      ...this.reportToolsProvider.getTools(),
      ...this.getKeyResultSupportTools(),
    ];
  }

  /**
   * Lấy KeyResultSupport tools
   */
  private getKeyResultSupportTools(): any[] {
    const { tool } = require('@langchain/core/tools');
    const { z } = require('zod');

    return [
      // Thêm Key Results hỗ trợ
      tool(
        async (_args: any, config: any) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const createDto = {
              childIds: _args.childIds,
            };

            const supports = await this.keyResultSupportService.addSupports(
              tenantId,
              _args.parentId,
              createDto,
            );

            return `Đã thêm ${supports.length} Key Results hỗ trợ cho Key Result ID ${_args.parentId}`;
          } catch (error) {
            return `Thêm Key Results hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_key_result_supports',
          description: 'Thêm các Key Results hỗ trợ cho một Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
            childIds: z.array(z.number()).describe('Danh sách ID các Key Results hỗ trợ'),
          }),
        }
      ),

      // Lấy danh sách Key Results hỗ trợ
      tool(
        async (_args: any, config: any) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const supports = await this.keyResultSupportService.getSupports(
              tenantId,
              _args.parentId,
            );

            if (supports.length === 0) {
              return `Key Result ID ${_args.parentId} không có Key Results hỗ trợ nào`;
            }

            const supportList = supports.map(s =>
              `- ${s.childKeyResult?.title || 'N/A'} (ID: ${s.childId})`
            ).join('\n');

            return `Key Result ID ${_args.parentId} có ${supports.length} Key Results hỗ trợ:\n${supportList}`;
          } catch (error) {
            return `Lấy danh sách Key Results hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_key_result_supports',
          description: 'Lấy danh sách các Key Results hỗ trợ cho một Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
          }),
        }
      ),

      // Xóa Key Result hỗ trợ
      tool(
        async (_args: any, config: any) => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const removed = await this.keyResultSupportService.removeSupport(
              tenantId,
              _args.parentId,
              _args.childId,
            );

            if (removed) {
              return `Đã xóa Key Result ID ${_args.childId} khỏi danh sách hỗ trợ của Key Result ID ${_args.parentId}`;
            } else {
              return `Không tìm thấy mối quan hệ hỗ trợ giữa Key Result ${_args.parentId} và ${_args.childId}`;
            }
          } catch (error) {
            return `Xóa Key Result hỗ trợ thất bại: ${error.message}`;
          }
        },
        {
          name: 'remove_key_result_support',
          description: 'Xóa một Key Result khỏi danh sách hỗ trợ của Key Result chính',
          schema: z.object({
            parentId: z.number().describe('ID của Key Result chính'),
            childId: z.number().describe('ID của Key Result hỗ trợ cần xóa'),
          }),
        }
      ),
    ];
  }

  /**
   * Tạo system prompt cho Analytics Agent
   */
  createSystemPrompt(): string {
    return `Bạn là Analytics Agent - chuyên gia phân tích dữ liệu và báo cáo của hệ thống ERP RedAI.

CHỨC NĂNG CHÍNH:
- Thống kê tổng quan: nhân viên, dự án, hiệu suất
- Báo cáo Excel: danh sách nhân viên, chấm công, thống kê HR
- OKR Management: mục tiêu và kết quả chính
- Dashboard: visualization và monitoring
- Data Analysis: insights và recommendations
- Export Reports: tạo file Excel với styling và charts

BÁOCÁO EXCEL AVAILABLE:
- Báo cáo danh sách nhân viên (HR Employees Report)
- Báo cáo thống kê nhân sự (HR Statistics Report)
- Báo cáo chấm công chi tiết (Attendance Details Report)
- Báo cáo tổng hợp chấm công (Attendance Summary Report)

NGUYÊN TẮC LÀM VIỆC:
1. Luôn đảm bảo tenant isolation - chỉ phân tích dữ liệu của tenant hiện tại
2. Cung cấp insights có giá trị và actionable
3. Tạo báo cáo Excel với styling đẹp và charts phù hợp
4. Đảm bảo tính chính xác và cập nhật của số liệu
5. Hỗ trợ decision making với data-driven approach
6. Cung cấp download links có thời hạn cho báo cáo

TOOLS AVAILABLE:
- Statistics: employee stats, department performance, project metrics
- OKR management: objectives, key results, progress tracking
- Excel Reports: create_hr_employees_report, create_hr_statistics_report, create_attendance_details_report, create_attendance_summary_report
- Dashboard: real-time monitoring, KPI tracking

KHI USER YÊU CẦU BÁOCÁO:
- Hỏi rõ loại báo cáo cần tạo
- Xác nhận khoảng thời gian và bộ lọc
- Tạo báo cáo Excel với styling và charts
- Cung cấp download link và thống kê tóm tắt

Hãy phân tích yêu cầu của user và cung cấp insights có giá trị từ dữ liệu hoặc tạo báo cáo Excel theo yêu cầu.`;
  }
}
