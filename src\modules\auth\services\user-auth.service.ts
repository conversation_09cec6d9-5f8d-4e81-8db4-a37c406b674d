import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import { UserRepository } from '../repositories/user.repository'; // REMOVED: UserRepository đã được xóa
import { PermissionRepository } from '../repositories/permission.repository';
import { UserLoginDto } from '../dto/user-login.dto';
import { UserStatus } from '../enum/user-status.enum';
import { EncryptionService } from '@shared/services/encryption.service';
import { EmailService } from '@shared/services/email';
import { RedisService } from '@shared/services/redis.service';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { AppException } from '@/common/exceptions/app.exception';
import { JwtUtilService, TokenType } from '../guards/jwt.util';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  UserLoginResponseDto,
  UserResponseDto,
} from '../dto/user-response.dto';
import {
  ChangePasswordDto,
  ChangePasswordResponseDto,
} from '../dto/change-password.dto';
import {
  AdminChangePasswordDto,
  AdminChangePasswordResponseDto,
} from '../dto/admin-change-password.dto';
import {
  ForgotPasswordDto,
  ForgotPasswordResponseDto,
  ResetPasswordDto,
  ResetPasswordResponseDto,
} from '../dto/reset-password.dto';
import {
  RegisterEmployeeDto,
  RegisterEmployeeResponseDto,
} from '../dto/register-employee.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import {
  AvatarUploadUrlResponseDto,
  UpdateUserAvatarDto,
} from '../dto/update-user-avatar.dto';
import { FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils/file';
import { EmployeeRepository } from '@/modules/hrm/employees/repositories/employee.repository';
import { EmployeeAuthService } from '@/modules/hrm/employees/services/employee-auth.service';

/**
 * Service xử lý authentication cho tài khoản người dùng
 * DEPRECATED: Sẽ được thay thế bởi EmployeeAuthService
 * Hiện tại đang delegate các method sang EmployeeAuthService
 */
@Injectable()
export class UserAuthService {
  private readonly logger = new Logger(UserAuthService.name);

  constructor(
    // private readonly userRepository: UserRepository, // REMOVED: UserRepository đã được xóa
    private readonly permissionRepository: PermissionRepository,
    private readonly encryptionService: EncryptionService,
    private readonly jwtService: JwtUtilService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly redisService: RedisService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeRepository: EmployeeRepository,
    private readonly employeeAuthService: EmployeeAuthService,
  ) {}

  /**
   * Đăng nhập tài khoản người dùng
   * DEPRECATED: Delegate sang EmployeeAuthService
   * @param loginDto Thông tin đăng nhập
   * @returns Token và thông tin người dùng
   */
  async login(
    loginDto: UserLoginDto,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    console.log('🔄 [USER AUTH] Delegating login to EmployeeAuthService');

    // Delegate sang EmployeeAuthService
    return this.employeeAuthService.login(loginDto);
  }

  /**
   * Lấy thông tin người dùng từ token
   * DEPRECATED: Delegate sang EmployeeAuthService
   * @param userId ID của người dùng
   * @returns Thông tin người dùng và danh sách quyền
   */
  async getUserProfile(
    userId: number,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    console.log('🔄 [USER AUTH] Delegating getUserProfile to EmployeeAuthService');

    // Delegate sang EmployeeAuthService
    return this.employeeAuthService.getEmployeeProfile(userId);
  }

  /**
   * Đổi mật khẩu cho người dùng đã đăng nhập
   * DEPRECATED: Delegate sang EmployeeAuthService
   * @param userId ID của người dùng
   * @param changePasswordDto Thông tin đổi mật khẩu
   * @returns Thông báo đổi mật khẩu thành công
   */
  async changePassword(
    userId: number,
    changePasswordDto: ChangePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    console.log('🔄 [USER AUTH] Delegating changePassword to EmployeeAuthService');

    // Delegate sang EmployeeAuthService
    return this.employeeAuthService.changePassword(userId, changePasswordDto);
  }

  /**
   * Đổi mật khẩu cho người dùng bởi admin (không cần mật khẩu cũ)
   * DEPRECATED: Delegate sang EmployeeAuthService
   * @param adminChangePasswordDto Thông tin đổi mật khẩu bởi admin
   * @returns Thông báo đổi mật khẩu thành công
   */
  async adminChangePassword(
    adminChangePasswordDto: AdminChangePasswordDto,
  ): Promise<ApiResponseDto<AdminChangePasswordResponseDto>> {
    console.log('🔄 [USER AUTH] Delegating adminChangePassword to EmployeeAuthService');

    // Delegate sang EmployeeAuthService
    return this.employeeAuthService.adminChangePassword(adminChangePasswordDto);
  }

  /**
   * Gửi email đặt lại mật khẩu
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param forgotPasswordDto Thông tin quên mật khẩu
   * @returns Thông báo gửi email thành công
   */
  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /* DEPRECATED METHODS - UserRepository đã bị xóa, các method này không hoạt động
  async forgotPasswordOld(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    // Tìm người dùng theo email
    const user = await this.userRepository.findByEmail(forgotPasswordDto.email);
    if (!user) {
      // Không thông báo lỗi cụ thể để tránh lộ thông tin
      const response: ForgotPasswordResponseDto = {
        message:
          'Nếu email tồn tại trong hệ thống, bạn sẽ nhận được email đặt lại mật khẩu.',
      };
      return ApiResponseDto.success(response);
    }

    // Tạo token đặt lại mật khẩu
    const resetToken = this.encryptionService.generateRandomToken();

    // Lưu token vào Redis với thời hạn 1 giờ
    const resetKey = `password_reset:${resetToken}`;
    await this.redisService.setWithExpiry(
      resetKey,
      user.id.toString(),
      60 * 60, // 1 giờ
    );

    // Tạo URL đặt lại mật khẩu
    const resetUrl = `${this.configService.get<string>(
      'APP_URL',
      'http://localhost:3000',
    )}/reset-password?token=${resetToken}`;

    // Tạo nội dung email
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Đặt lại mật khẩu</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
          }
          .header {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
          }
          .content {
            padding: 20px;
          }
          .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
          }
          .url-display {
            word-break: break-all;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Đặt lại mật khẩu</h2>
          </div>
          <div class="content">
            <p>Xin chào,</p>
            <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng nhấp vào nút bên dưới để đặt lại mật khẩu:</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="${resetUrl}" class="button">Đặt lại mật khẩu</a>
            </div>
            <p>Hoặc bạn có thể sao chép và dán liên kết sau vào trình duyệt của bạn:</p>
            <div class="url-display">
              ${resetUrl}
            </div>
            <p>Liên kết này sẽ hết hạn sau 1 giờ.</p>
            <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
            <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} RedAI. Tất cả các quyền được bảo lưu.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Gửi email
    try {
      await this.emailService.sendEmail({
        to: user.email,
        subject: 'Đặt lại mật khẩu',
        html,
      });
    } catch (error) {
      this.logger.error(
        `Failed to send reset password email: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.EMAIL_SENDING_ERROR,
        'Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.',
      );
    }

    const response: ForgotPasswordResponseDto = {
      message:
        'Email đặt lại mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.',
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Đặt lại mật khẩu
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param resetPasswordDto Thông tin đặt lại mật khẩu
   * @returns Thông báo đặt lại mật khẩu thành công
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<ApiResponseDto<ResetPasswordResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /**
   * Đăng ký tài khoản nhân viên mới
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param registerDto Thông tin đăng ký
   * @returns Thông báo đăng ký thành công
   */
  async registerEmployee(
    registerDto: RegisterEmployeeDto,
  ): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /**
   * Cập nhật thông tin cá nhân của người dùng
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param userId ID của người dùng
   * @param updateProfileDto Thông tin cần cập nhật
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUserProfile(
    userId: number,
    updateProfileDto: UpdateUserProfileDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /**
   * Tạo URL tạm thời để upload avatar
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param userId ID của người dùng
   * @returns URL tạm thời và key
   */
  async createAvatarUploadUrl(
    userId: number,
  ): Promise<ApiResponseDto<AvatarUploadUrlResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /**
   * Cập nhật avatar của người dùng
   * DEPRECATED: UserRepository đã bị xóa, method này không hoạt động
   * @param userId ID của người dùng
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUserAvatar(
    userId: number,
    updateAvatarDto: UpdateUserAvatarDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    throw new AppException(
      AUTH_ERROR_CODE.USER_NOT_FOUND,
      'Chức năng này đã được chuyển sang EmployeeAuthService. Vui lòng sử dụng endpoint mới.',
    );
  }

  /* DEPRECATED CODE - UserRepository đã bị xóa, tất cả code cũ đã được comment out
  Sử dụng EmployeeAuthService thay thế cho tất cả chức năng liên quan đến authentication
  */
}
