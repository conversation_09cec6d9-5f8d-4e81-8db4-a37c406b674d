-- =====================================================
-- MIGRATION 003: Cleanup và xóa bảng USERS
-- Mục tiêu: Ho<PERSON>n tất việc gộp Users vào Employees
-- Ngày: 2025-06-18
-- =====================================================

-- =====================================================
-- PHASE 1: VERIFICATION TRƯỚC KHI XÓA
-- =====================================================

-- <PERSON><PERSON><PERSON> tra tất cả foreign key references đã được cập nhật
DO $$
DECLARE
    remaining_refs INTEGER;
BEGIN
    -- Kiểm tra còn bao nhiêu references đến users table
    SELECT COUNT(*) INTO remaining_refs
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND ccu.table_name = 'users';
    
    IF remaining_refs > 0 THEN
        RAISE NOTICE 'WARNING: Still have % foreign key references to users table', remaining_refs;
    ELSE
        RAISE NOTICE 'OK: No foreign key references to users table found';
    END IF;
END $$;

-- Kiểm tra migration log
SELECT 
    'MIGRATION VERIFICATION' as check_type,
    migration_method,
    migration_status,
    COUNT(*) as count,
    STRING_AGG(CASE WHEN error_message IS NOT NULL THEN 'ID:' || old_user_id || '-' || error_message END, '; ') as errors
FROM user_employee_migration_log
GROUP BY migration_method, migration_status
ORDER BY migration_method, migration_status;

-- Kiểm tra employees có đầy đủ authentication data
SELECT 
    'EMPLOYEES AUTHENTICATION CHECK' as check_type,
    COUNT(*) as total_employees,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email,
    COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as has_password,
    COUNT(CASE WHEN account_status IS NOT NULL THEN 1 END) as has_account_status,
    COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as has_tenant_id,
    COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_accounts
FROM employees;

-- =====================================================
-- PHASE 2: XÓA FOREIGN KEY CONSTRAINTS ĐẾN USERS
-- =====================================================

-- Xóa tất cả foreign key constraints reference đến users table
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'users'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
END $$;

-- =====================================================
-- PHASE 3: RENAME COLUMNS user_id -> employee_id (OPTIONAL)
-- =====================================================

-- Rename columns để rõ ràng hơn (optional - có thể giữ nguyên để tương thích code)
-- Uncomment các dòng dưới nếu muốn rename columns

-- ALTER TABLE user_roles RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE business_info RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE two_factor_auth RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE device_info RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE auth_verification_logs RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE point_purchase_transactions RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE affiliate_accounts RENAME COLUMN user_id TO employee_id;
-- ALTER TABLE affiliate_contracts RENAME COLUMN user_id TO employee_id;

-- Cập nhật comments cho các bảng đã rename (nếu rename)
-- COMMENT ON COLUMN user_roles.employee_id IS 'ID nhân viên (thay thế user_id)';
-- COMMENT ON COLUMN business_info.employee_id IS 'ID nhân viên (thay thế user_id)';

-- =====================================================
-- PHASE 4: TẠO FOREIGN KEY CONSTRAINTS MỚI ĐẾN EMPLOYEES
-- =====================================================

-- Tạo foreign key constraints mới reference đến employees table
ALTER TABLE user_roles 
ADD CONSTRAINT user_roles_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE business_info 
ADD CONSTRAINT business_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE two_factor_auth 
ADD CONSTRAINT two_factor_auth_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE device_info 
ADD CONSTRAINT device_info_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE auth_verification_logs 
ADD CONSTRAINT auth_verification_logs_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE point_purchase_transactions 
ADD CONSTRAINT point_purchase_transactions_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE affiliate_accounts 
ADD CONSTRAINT affiliate_accounts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE affiliate_contracts 
ADD CONSTRAINT affiliate_contracts_employees_fk 
FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE;

-- =====================================================
-- PHASE 5: XÓA BẢNG USERS
-- =====================================================

-- Xóa bảng users (đã backup ở migration 002)
DROP TABLE IF EXISTS users CASCADE;

-- Xóa các enum types không còn sử dụng (nếu có)
-- DROP TYPE IF EXISTS user_type CASCADE;
-- DROP TYPE IF EXISTS gender CASCADE; -- Chỉ xóa nếu không dùng ở chỗ khác

-- =====================================================
-- PHASE 6: TẠO VIEWS ĐỂ TƯƠNG THÍCH NGƯỢC (OPTIONAL)
-- =====================================================

-- Tạo view users để tương thích với code cũ (optional)
CREATE OR REPLACE VIEW users AS
SELECT 
    id,
    employee_name as full_name,
    email,
    NULL as phone_number, -- Không có trong employees
    CASE 
        WHEN account_status = 'ACTIVE' THEN true
        ELSE false
    END as is_active,
    false as is_verify_email, -- Default value
    created_at,
    updated_at,
    NULL as citizen_issue_place,
    NULL as citizen_issue_date,
    false as is_first_password_change,
    NULL as country,
    NULL as address,
    tax_id as tax_code,
    0 as points_balance, -- Default value
    'INDIVIDUAL' as type, -- Default value
    NULL as platform,
    NULL as citizen_id,
    NULL as avatar,
    password,
    date_of_birth,
    gender,
    NULL as bank_code,
    bank_account_number as account_number,
    NULL as account_holder,
    NULL as bank_branch,
    'employee' as role, -- Default role
    false as is_verify_phone,
    NULL as vector_store_key,
    NULL as alert_threshold,
    false as was_rpoint_alerted,
    tenant_id
FROM employees;

-- Cập nhật comment cho view
COMMENT ON VIEW users IS 'View tương thích ngược - map employees data sang users format';

-- =====================================================
-- PHASE 7: FINAL VERIFICATION
-- =====================================================

-- Kiểm tra cấu trúc cuối cùng
SELECT 
    'FINAL VERIFICATION' as check_type,
    'employees_table' as item,
    COUNT(*) as count
FROM employees

UNION ALL

SELECT 
    'FINAL VERIFICATION' as check_type,
    'users_view' as item,
    COUNT(*) as count
FROM users

UNION ALL

SELECT 
    'FINAL VERIFICATION' as check_type,
    'migration_log' as item,
    COUNT(*) as count
FROM user_employee_migration_log;

-- Kiểm tra foreign key constraints mới
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND ccu.table_name = 'employees'
ORDER BY tc.table_name, tc.constraint_name;

-- Tạo final summary report
SELECT 
    'MIGRATION COMPLETED SUCCESSFULLY' as status,
    (SELECT COUNT(*) FROM employees) as total_employees,
    (SELECT COUNT(*) FROM employees WHERE account_status = 'ACTIVE') as active_employees,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'success') as successful_migrations,
    (SELECT COUNT(*) FROM user_employee_migration_log WHERE migration_status = 'failed') as failed_migrations,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') 
        THEN 'users table still exists' 
        ELSE 'users table successfully removed' 
    END as users_table_status;

COMMIT;
