#!/usr/bin/env node

/**
 * Test script để verify việ<PERSON> gộp User vào Employee đã thành công
 */

const { Client } = require('pg');

async function testUserEmployeeMerge() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'ai_erp',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Test 1: Verify users table không còn tồn tại
    console.log('\n📋 Test 1: Verify users table đã được xóa');
    try {
      await client.query('SELECT COUNT(*) FROM users');
      console.log('❌ FAIL: Users table vẫn còn tồn tại');
      return false;
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('✅ PASS: Users table đã được xóa thành công');
      } else {
        console.log('❌ FAIL: Lỗi không mong đợi:', error.message);
        return false;
      }
    }

    // Test 2: Verify employees có authentication data
    console.log('\n📋 Test 2: Verify employees có authentication data');
    const authResult = await client.query(`
      SELECT 
        COUNT(*) as total_employees,
        COUNT(CASE WHEN email IS NOT NULL AND password IS NOT NULL THEN 1 END) as employees_with_auth,
        COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_employees
      FROM employees
    `);
    
    const { total_employees, employees_with_auth, active_employees } = authResult.rows[0];
    console.log(`📊 Total employees: ${total_employees}`);
    console.log(`🔐 Employees with auth: ${employees_with_auth}`);
    console.log(`✅ Active employees: ${active_employees}`);
    
    if (employees_with_auth > 0) {
      console.log('✅ PASS: Employees có authentication data');
    } else {
      console.log('❌ FAIL: Không có employee nào có authentication data');
      return false;
    }

    // Test 3: Verify foreign key references
    console.log('\n📋 Test 3: Verify foreign key references');
    
    const foreignKeyTables = [
      'user_roles',
      'user_has_permissions',
      'social_accounts'
    ];

    for (const tableName of foreignKeyTables) {
      try {
        const result = await client.query(`
          SELECT COUNT(*) as total_records,
                 COUNT(CASE WHEN e.id IS NOT NULL THEN 1 END) as valid_references
          FROM ${tableName} t
          LEFT JOIN employees e ON t.user_id = e.id
        `);
        
        const { total_records, valid_references } = result.rows[0];
        console.log(`📋 ${tableName}: ${valid_references}/${total_records} valid references`);
        
        if (total_records > 0 && valid_references !== total_records) {
          console.log(`❌ FAIL: ${tableName} có broken references`);
          return false;
        } else if (total_records > 0) {
          console.log(`✅ PASS: ${tableName} foreign keys OK`);
        }
      } catch (error) {
        console.log(`⚠️  ${tableName}: Table không tồn tại hoặc lỗi - ${error.message}`);
      }
    }

    // Test 4: Verify indexes đã được tạo
    console.log('\n📋 Test 4: Verify indexes cho authentication');
    const indexResult = await client.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'employees' 
      AND (indexname LIKE '%email%' OR indexname LIKE '%account_status%')
    `);
    
    console.log('📊 Authentication indexes:');
    indexResult.rows.forEach(row => {
      console.log(`  - ${row.indexname}`);
    });
    
    if (indexResult.rows.length > 0) {
      console.log('✅ PASS: Authentication indexes đã được tạo');
    } else {
      console.log('⚠️  WARNING: Không tìm thấy authentication indexes');
    }

    // Test 5: Test authentication query performance
    console.log('\n📋 Test 5: Test authentication query');
    const authTestResult = await client.query(`
      SELECT id, email, account_status, password IS NOT NULL as has_password
      FROM employees 
      WHERE email IS NOT NULL 
      AND account_status = 'ACTIVE'
      LIMIT 5
    `);
    
    console.log('📊 Sample authentication data:');
    authTestResult.rows.forEach(row => {
      console.log(`  - ID: ${row.id}, Email: ${row.email}, Status: ${row.account_status}, Has Password: ${row.has_password}`);
    });
    
    if (authTestResult.rows.length > 0) {
      console.log('✅ PASS: Authentication queries hoạt động');
    } else {
      console.log('⚠️  WARNING: Không có employee nào có thể authenticate');
    }

    // Test 6: Verify backup table
    console.log('\n📋 Test 6: Verify backup table');
    try {
      const backupResult = await client.query('SELECT COUNT(*) as backup_count FROM users_backup');
      const backupCount = backupResult.rows[0].backup_count;
      console.log(`📊 Users backup records: ${backupCount}`);
      
      if (backupCount > 0) {
        console.log('✅ PASS: Users backup table tồn tại và có dữ liệu');
      } else {
        console.log('⚠️  WARNING: Users backup table rỗng');
      }
    } catch (error) {
      console.log('⚠️  WARNING: Users backup table không tồn tại');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Users table đã được xóa');
    console.log('✅ Employees có authentication data');
    console.log('✅ Foreign key references đã được cập nhật');
    console.log('✅ Authentication indexes đã được tạo');
    console.log('✅ Authentication queries hoạt động');
    
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Chạy test
if (require.main === module) {
  testUserEmployeeMerge()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test script error:', error);
      process.exit(1);
    });
}

module.exports = { testUserEmployeeMerge };
