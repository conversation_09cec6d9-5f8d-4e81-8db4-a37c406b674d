-- =====================================================
-- FINAL USER-EMPLOYEE MERGE MIGRATION
-- Gộp hoàn toàn User entity vào Employee entity và xóa users table
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- =====================================================
-- PHASE 1: BACKUP VÀ VALIDATION
-- =====================================================

-- Tạo backup table trước khi xóa
CREATE TABLE users_backup AS SELECT * FROM users;

-- Validation: <PERSON><PERSON><PERSON> bảo tất cả users đã có employee tương ứng
DO $$
DECLARE
    orphaned_users_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO orphaned_users_count
    FROM users u
    LEFT JOIN employees e ON LOWER(u.email) = LOWER(e.email)
    WHERE e.id IS NULL;
    
    IF orphaned_users_count > 0 THEN
        RAISE EXCEPTION 'Found % users without corresponding employees. Migration aborted.', orphaned_users_count;
    END IF;
    
    RAISE NOTICE 'Validation passed: All users have corresponding employees';
END $$;

-- =====================================================
-- PHASE 2: FINAL FOREIGN KEY UPDATES
-- =====================================================

-- Tạo final mapping table
CREATE TEMP TABLE final_user_employee_mapping AS
SELECT 
    u.id as user_id,
    e.id as employee_id,
    u.email as user_email,
    e.email as employee_email
FROM users u
INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email);

-- Log mapping information
RAISE NOTICE 'Created mapping for % user-employee pairs', (SELECT COUNT(*) FROM final_user_employee_mapping);

-- Cập nhật tất cả foreign key references lần cuối
UPDATE user_roles ur
SET user_id = m.employee_id
FROM final_user_employee_mapping m
WHERE ur.user_id = m.user_id;

UPDATE user_has_permissions uhp
SET user_id = m.employee_id
FROM final_user_employee_mapping m
WHERE uhp.user_id = m.user_id;

UPDATE social_accounts sa
SET user_id = m.employee_id
FROM final_user_employee_mapping m
WHERE sa.user_id = m.user_id;

-- Cập nhật các bảng khác nếu tồn tại
DO $$
BEGIN
    -- Facebook personal
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'facebook_personal') THEN
        UPDATE facebook_personal fp
        SET user_id = m.employee_id
        FROM final_user_employee_mapping m
        WHERE fp.user_id = m.user_id;
    END IF;
    
    -- Facebook page configs
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'facebook_page_configs') THEN
        UPDATE facebook_page_configs fpc
        SET user_id = m.employee_id
        FROM final_user_employee_mapping m
        WHERE fpc.user_id = m.user_id;
    END IF;
    
    -- Todo comments
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'todo_comments') THEN
        UPDATE todo_comments tc
        SET user_id = m.employee_id
        FROM final_user_employee_mapping m
        WHERE tc.user_id = m.user_id;
    END IF;
    
    -- Project members
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_members') THEN
        UPDATE project_members pm
        SET user_id = m.employee_id
        FROM final_user_employee_mapping m
        WHERE pm.user_id = m.user_id;
    END IF;
END $$;

-- =====================================================
-- PHASE 3: FINAL VALIDATION
-- =====================================================

-- Kiểm tra foreign key integrity
DO $$
DECLARE
    broken_refs_count INTEGER;
BEGIN
    -- Check user_roles
    SELECT COUNT(*) INTO broken_refs_count
    FROM user_roles ur
    LEFT JOIN employees e ON ur.user_id = e.id
    WHERE e.id IS NULL;
    
    IF broken_refs_count > 0 THEN
        RAISE EXCEPTION 'Found % broken references in user_roles', broken_refs_count;
    END IF;
    
    -- Check user_has_permissions
    SELECT COUNT(*) INTO broken_refs_count
    FROM user_has_permissions uhp
    LEFT JOIN employees e ON uhp.user_id = e.id
    WHERE e.id IS NULL;
    
    IF broken_refs_count > 0 THEN
        RAISE EXCEPTION 'Found % broken references in user_has_permissions', broken_refs_count;
    END IF;
    
    RAISE NOTICE 'Foreign key integrity validation passed';
END $$;

-- =====================================================
-- PHASE 4: DROP USERS TABLE
-- =====================================================

-- Xóa users table
DROP TABLE IF EXISTS users CASCADE;

RAISE NOTICE 'Users table dropped successfully';

-- =====================================================
-- PHASE 5: CREATE FINAL INDEXES
-- =====================================================

-- Tạo indexes cho Employee authentication
CREATE INDEX IF NOT EXISTS idx_employees_email_auth ON employees(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_employees_account_status ON employees(account_status) WHERE account_status IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_employees_tenant_auth ON employees(tenant_id, email) WHERE email IS NOT NULL;

-- =====================================================
-- PHASE 6: FINAL LOGGING
-- =====================================================

-- Log migration completion
INSERT INTO migration_log (migration_name, status, completed_at, notes)
VALUES (
    'final-user-employee-merge',
    'completed',
    EXTRACT(epoch FROM now()) * 1000,
    'Successfully merged User entity into Employee entity and dropped users table'
) ON CONFLICT DO NOTHING;

RAISE NOTICE 'Final User-Employee merge migration completed successfully';

-- Commit transaction
COMMIT;

-- =====================================================
-- POST-MIGRATION VERIFICATION
-- =====================================================

-- Verify employees can authenticate
SELECT 
    COUNT(*) as total_employees,
    COUNT(CASE WHEN email IS NOT NULL AND password IS NOT NULL THEN 1 END) as employees_with_auth,
    COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active_employees
FROM employees;
