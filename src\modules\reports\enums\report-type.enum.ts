/**
 * Enum định nghĩa các loại báo cáo trong hệ thống
 */
export enum ReportType {
  // Báo cáo nhân sự
  HR_EMPLOYEES = 'hr_employees',
  HR_STATISTICS = 'hr_statistics',
  HR_DEPARTMENT_OVERVIEW = 'hr_department_overview',
  
  // Báo cáo chấm công
  ATTENDANCE_DETAILS = 'attendance_details',
  ATTENDANCE_SUMMARY = 'attendance_summary',
  ATTENDANCE_OVERTIME = 'attendance_overtime',
  ATTENDANCE_LATE_EARLY = 'attendance_late_early',
  
  // Báo cáo OKR
  OKR_PROGRESS = 'okr_progress',
  OKR_STATISTICS = 'okr_statistics',
  OKR_DEPARTMENT_SUMMARY = 'okr_department_summary',
  
  // Báo cáo dự án và công việc
  PROJECT_PROGRESS = 'project_progress',
  PROJECT_PERFORMANCE = 'project_performance',
  TODO_SUMMARY = 'todo_summary',
  TODO_RATING = 'todo_rating',
  
  // Báo cáo tùy chỉnh
  CUSTOM = 'custom'
}

/**
 * Enum định nghĩa format xuất báo cáo
 */
export enum ReportFormat {
  XLSX = 'xlsx',
  CSV = 'csv',
  PDF = 'pdf'
}

/**
 * Enum định nghĩa trạng thái báo cáo
 */
export enum ReportStatus {
  PENDING = 'pending',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  EXPIRED = 'expired'
}

/**
 * Enum định nghĩa category báo cáo
 */
export enum ReportCategory {
  HR = 'hr',
  ATTENDANCE = 'attendance',
  OKR = 'okr',
  PROJECT = 'project',
  FINANCE = 'finance',
  ANALYTICS = 'analytics'
}
