import { Injectable } from '@nestjs/common';
import { TodoService } from '@modules/todolists/services/todo.service';
import { UserService } from '@/modules/hrm/employees/services/user.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { TodoPriority } from '@modules/todolists/enum/todo-priority.enum';
import { TodoStatus } from '@modules/todolists/enum/todo-status.enum';

/**
 * Todo Tools Provider
 * Cung cấp các tools liên quan đến quản lý công việc
 */
@Injectable()
export class TodoToolsProvider {
  constructor(
    private readonly todoService: TodoService,
    private readonly userService: UserService,
  ) {}

  /**
   * Lấy tất cả todo tools
   */
  getTools() {
    return [
      // Tạo công việc mới
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createTodoDto = {
              title: _args.title,
              description: _args.description,
              priority: _args.priority as TodoPriority,
              deadline: _args.deadline ? new Date(_args.deadline).getTime() : undefined,
              assigneeId: _args.assigneeId || userId,
              categoryId: _args.projectId,
              parentId: _args.parentId,
            };

            const todo = await this.todoService.createTodo(tenantId, userId, createTodoDto);
            return `Công việc "${todo.title}" đã được tạo thành công với ID: ${todo.id}`;
          } catch (error) {
            return `Tạo công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_todo',
          description: 'Tạo công việc mới',
          schema: z.object({
            title: z.string().nonempty().describe('Tiêu đề công việc'),
            description: z.string().optional().describe('Mô tả chi tiết công việc'),
            priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().default('medium').describe('Mức độ ưu tiên'),
            deadline: z.string().optional().describe('Thời gian deadline (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm)'),
            assigneeId: z.number().optional().describe('ID người được giao việc'),
            projectId: z.number().optional().describe('ID dự án'),
            parentId: z.number().optional().describe('ID công việc cha (nếu là subtask)'),
          }),
        }
      ),

      // Lấy danh sách công việc của người dùng
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(config?.configurable?.['userId'] || '0');

            // Sử dụng findAllTodos thay vì getUserTasks để hỗ trợ tagIds
            const query = {
              assigneeId: userId,
              status: _args.status as any,
              priority: _args.priority as any,
              tagIds: _args.tagIds,
              page: 1,
              limit: _args.limit || 10,
            };

            const result = await this.todoService.findAllTodos(tenantId, query);
            const tasks = result.items || [];

            return `Tìm thấy ${tasks.length} công việc của người dùng:\n${JSON.stringify(tasks, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_user_todos',
          description: 'Lấy danh sách công việc của người dùng',
          schema: z.object({
            userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
            status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Trạng thái công việc'),
            priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Mức độ ưu tiên'),
            tagIds: z.array(z.number()).optional().describe('Lọc theo danh sách tag IDs'),
            limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy công việc quá hạn
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(config?.configurable?.['userId'] || '0');

            const overdueTasks = await this.todoService.getOverdueTasks(tenantId, {
              assigneeId: userId,
              limit: _args.limit,
            });

            return `Tìm thấy ${overdueTasks.length} công việc quá hạn:\n${JSON.stringify(overdueTasks, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách công việc quá hạn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_overdue_todos',
          description: 'Lấy danh sách công việc quá hạn',
          schema: z.object({
            userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy chi tiết công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const taskDetails = await this.todoService.findTodoById(tenantId, _args.todoId);

            if (!taskDetails) {
              return 'Không tìm thấy công việc';
            }

            let result = `📋 CHI TIẾT CÔNG VIỆC:
🏷️ Tiêu đề: ${taskDetails.title}
📝 Mô tả: ${taskDetails.description || 'Không có'}
👤 Người được giao: ${taskDetails.assigneeId ? `ID: ${taskDetails.assigneeId}` : 'Chưa giao'}
📊 Trạng thái: ${taskDetails.status || 'Không có'}
⭐ Ưu tiên: ${taskDetails.priority || 'Không có'}
🎯 Sao kỳ vọng: ${taskDetails.expectedStars || 'Không có'}
🏆 Sao đánh giá: ${taskDetails.awardedStars || 'Chưa chấm'}
📅 Ngày tạo: ${taskDetails.createdAt ? new Date(taskDetails.createdAt).toLocaleString('vi-VN') : 'Không có'}
⏰ Deadline: ${taskDetails.deadline ? new Date(taskDetails.deadline).toLocaleString('vi-VN') : 'Không có'}
✅ Hoàn thành: ${taskDetails.completedAt ? new Date(taskDetails.completedAt).toLocaleString('vi-VN') : 'Chưa hoàn thành'}`;

            // Lấy subtasks nếu được yêu cầu
            if (_args.includeSubtasks) {
              try {
                const subtasksResult = await this.todoService.findSubtasks(tenantId, _args.todoId, { page: 1, limit: 10 });
                const subtasks = subtasksResult.items || [];
                result += `\n\n📋 CÔNG VIỆC CON (${subtasks.length}):`;
                subtasks.forEach((subtask: any, index: number) => {
                  result += `\n${index + 1}. ${subtask.title} - ${subtask.status || 'pending'}`;
                });
              } catch (error) {
                result += '\n\n❌ Không thể lấy danh sách công việc con';
              }
            }

            return result;
          } catch (error) {
            return `Lấy chi tiết công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_details',
          description: 'Lấy chi tiết công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            includeComments: z.boolean().optional().default(false).describe('Bao gồm bình luận (chưa hỗ trợ)'),
            includeSubtasks: z.boolean().optional().default(false).describe('Bao gồm công việc con'),
          }),
        }
      ),

      // Cập nhật trạng thái công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateStatusDto = {
              status: _args.status as TodoStatus,
            };

            const updatedTodo = await this.todoService.updateTodoStatus(
              tenantId,
              _args.todoId,
              userId,
              updateStatusDto,
            );

            if (!updatedTodo) {
              return `Không tìm thấy công việc với ID ${_args.todoId}`;
            }

            const statusText = {
              'pending': 'Chờ xử lý',
              'in_progress': 'Đang thực hiện',
              'completed': 'Hoàn thành',
              'cancelled': 'Đã hủy',
              'approved': 'Đã duyệt',
              'rejected': 'Từ chối'
            }[_args.status] || _args.status;

            return `✅ Trạng thái công việc "${updatedTodo.title}" đã được cập nhật thành: ${statusText}`;
          } catch (error) {
            return `Cập nhật trạng thái công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_todo_status',
          description: 'Cập nhật trạng thái công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            status: z.enum(['pending', 'in_progress', 'completed', 'cancelled', 'approved', 'rejected']).describe('Trạng thái mới'),
          }),
        }
      ),

      // Gán công việc cho người khác (sử dụng unified User.id = Employee.id)
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            // Với unified ID system, assigneeId có thể là User.id hoặc Employee.id (giống nhau)
            // Kiểm tra user/employee tồn tại (vì User.id = Employee.id)
            try {
              // Kiểm tra trong bảng users (vì có foreign key constraint)
              const userExists = await this.userService.findUserById(tenantId, _args.assigneeId);
              if (!userExists) {
                return `❌ Không tìm thấy người dùng/nhân viên với ID ${_args.assigneeId} trong hệ thống

💡 LƯU Ý: Với hệ thống unified ID, User.id = Employee.id
   Vui lòng kiểm tra lại ID người dùng/nhân viên`;
              }
            } catch (checkError) {
              return `❌ Không thể kiểm tra người dùng: ${checkError.message}`;
            }

            const updateDto = {
              assigneeId: _args.assigneeId, // Unified ID: có thể assign cho User.id = Employee.id
            };

            const updatedTodo = await this.todoService.updateTodo(tenantId, _args.todoId, userId, updateDto);

            if (!updatedTodo) {
              return `❌ Không tìm thấy công việc với ID ${_args.todoId}`;
            }

            return `✅ Công việc "${updatedTodo.title}" đã được gán cho người dùng/nhân viên ID: ${_args.assigneeId}

📋 THÔNG TIN CHI TIẾT:
• ID công việc: ${_args.todoId}
• Tiêu đề: ${updatedTodo.title}
• Người được gán: Unified ID ${_args.assigneeId} (User = Employee)
• Trạng thái: ${updatedTodo.status}
• Thời gian cập nhật: ${new Date().toLocaleString('vi-VN')}

🔗 UNIFIED ID SYSTEM:
• User.id = Employee.id = ${_args.assigneeId}
• Một ID duy nhất cho cả authentication và HR data`;
          } catch (error) {
            if (error.message.includes('foreign key constraint')) {
              return `❌ Gán công việc thất bại: ID ${_args.assigneeId} không tồn tại trong hệ thống

🔍 NGUYÊN NHÂN:
• Với unified ID system, assigneeId phải tồn tại trong cả bảng users và employees
• ID ${_args.assigneeId} không có trong hệ thống hoặc không thuộc cùng tenant

💡 HƯỚNG DẪN:
• Kiểm tra danh sách người dùng/nhân viên có sẵn
• Đảm bảo ID thuộc cùng công ty (tenant)
• Liên hệ admin nếu cần tạo tài khoản mới`;
            }
            return `❌ Gán công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'assign_todo',
          description: 'Gán công việc cho người khác',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            assigneeId: z.number().describe('ID người được giao việc'),
          }),
        }
      ),

      // Chấm điểm công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const scoreTodoDto = {
              awardedStars: _args.awardedStars,
              feedback: _args.feedback,
            };

            const updatedTodo = await this.todoService.scoreTodo(
              tenantId,
              _args.todoId,
              userId,
              scoreTodoDto,
            );

            if (!updatedTodo) {
              return `Không tìm thấy công việc với ID ${_args.todoId}`;
            }

            return `✅ Đã chấm điểm thành công cho công việc "${updatedTodo.title}": ${_args.awardedStars}/5 sao${_args.feedback ? `\n💬 Phản hồi: ${_args.feedback}` : ''}`;
          } catch (error) {
            return `Chấm điểm công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'award_todo_stars',
          description: 'Chấm điểm cho công việc (chỉ supervisor mới có quyền)',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            awardedStars: z.number().min(1).max(5).describe('Số sao được trao (1-5)'),
            feedback: z.string().optional().describe('Phản hồi/nhận xét về công việc'),
          }),
        }
      ),

      // Lấy danh sách công việc con
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            // Sử dụng findSubtasks để lấy subtasks
            const query = {
              page: 1,
              limit: _args.limit || 20,
            };

            const result = await this.todoService.findSubtasks(tenantId, _args.todoId, query);
            const subtasks = result.items || [];

            // Lọc theo trạng thái nếu cần
            let filteredSubtasks = subtasks;
            if (!_args.includeCompleted) {
              filteredSubtasks = subtasks.filter((task: any) => task.status !== 'completed');
            }

            return `Tìm thấy ${filteredSubtasks.length} công việc con:\n${JSON.stringify(filteredSubtasks, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách công việc con thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_subtasks',
          description: 'Lấy danh sách công việc con',
          schema: z.object({
            todoId: z.number().describe('ID công việc cha'),
            includeCompleted: z.boolean().optional().default(true).describe('Bao gồm công việc đã hoàn thành'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy công việc theo dự án
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            // Sử dụng findAllTodos với projectId filter
            const query = {
              categoryId: _args.projectId, // Sử dụng categoryId thay vì projectId
              status: _args.status as any,
              assigneeId: _args.assigneeId,
              tagIds: _args.tagIds,
              page: 1,
              limit: _args.limit || 20,
            };

            const result = await this.todoService.findAllTodos(tenantId, query);
            const todos = result.items || [];

            return `Tìm thấy ${todos.length} công việc trong dự án:\n${JSON.stringify(todos, null, 2)}`;
          } catch (error) {
            return `Lấy công việc theo dự án thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todos_by_project',
          description: 'Lấy danh sách công việc theo dự án',
          schema: z.object({
            projectId: z.number().describe('ID dự án'),
            status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Lọc theo trạng thái'),
            assigneeId: z.number().optional().describe('Lọc theo người được giao việc'),
            tagIds: z.array(z.number()).optional().describe('Lọc theo danh sách tag IDs'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Lấy công việc theo tags
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              tagIds: _args.tagIds,
              status: _args.status as any,
              priority: _args.priority as any,
              assigneeId: _args.assigneeId,
              page: 1,
              limit: _args.limit || 20,
            };

            const result = await this.todoService.findAllTodos(tenantId, query);
            const todos = result.items || [];

            const tagNames = todos.length > 0 && todos[0].tags
              ? todos[0].tags.map((tag: any) => tag.name).join(', ')
              : 'N/A';

            return `Tìm thấy ${todos.length} công việc có tags [${tagNames}]:\n${JSON.stringify(todos, null, 2)}`;
          } catch (error) {
            return `Lấy công việc theo tags thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todos_by_tags',
          description: 'Lấy danh sách công việc theo tags',
          schema: z.object({
            tagIds: z.array(z.number()).describe('Danh sách tag IDs cần lọc'),
            status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Lọc theo trạng thái'),
            priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Lọc theo mức độ ưu tiên'),
            assigneeId: z.number().optional().describe('Lọc theo người được giao việc'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Tìm kiếm công việc với nhiều filter
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              search: _args.search,
              status: _args.status as any,
              priority: _args.priority as any,
              assigneeId: _args.assigneeId,
              categoryId: _args.projectId,
              tagIds: _args.tagIds,
              page: _args.page || 1,
              limit: _args.limit || 20,
            };

            const result = await this.todoService.findAllTodos(tenantId, query);
            const todos = result.items || [];

            return `Tìm thấy ${todos.length}/${result.meta?.totalItems || 0} công việc (trang ${result.meta?.currentPage || 1}/${result.meta?.totalPages || 1}):\n${JSON.stringify(todos, null, 2)}`;
          } catch (error) {
            return `Tìm kiếm công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'search_todos',
          description: 'Tìm kiếm công việc với nhiều bộ lọc',
          schema: z.object({
            search: z.string().optional().describe('Từ khóa tìm kiếm trong tiêu đề và mô tả'),
            status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Lọc theo trạng thái'),
            priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Lọc theo mức độ ưu tiên'),
            assigneeId: z.number().optional().describe('Lọc theo người được giao việc'),
            projectId: z.number().optional().describe('Lọc theo dự án'),
            tagIds: z.array(z.number()).optional().describe('Lọc theo danh sách tag IDs'),
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
          }),
        }
      ),

      // Thiết lập deadline cho công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const deadline = new Date(_args.deadline).getTime();
            const setDeadlineDto = { deadline };

            const updatedTodo = await this.todoService.setDeadline(
              tenantId,
              _args.todoId,
              userId,
              setDeadlineDto,
            );

            if (!updatedTodo) {
              return `Không tìm thấy công việc với ID ${_args.todoId}`;
            }

            const deadlineDate = new Date(deadline).toLocaleString('vi-VN');
            return `Đã thiết lập deadline cho công việc "${updatedTodo.title}" vào ${deadlineDate}`;
          } catch (error) {
            return `Thiết lập deadline thất bại: ${error.message}`;
          }
        },
        {
          name: 'set_todo_deadline',
          description: 'Thiết lập deadline cho công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            deadline: z.string().describe('Thời gian deadline (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm)'),
          }),
        }
      ),

      // Lấy thống kê deadline
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              projectId: _args.projectId,
              assigneeId: _args.assigneeId,
              tags: _args.tags,
              warningDays: _args.warningDays || 3,
            };

            const statistics = await this.todoService.getDeadlineStatistics(tenantId, query);

            const summary = `📊 THỐNG KÊ DEADLINE:
📋 Tổng công việc có deadline: ${statistics.totalWithDeadline}
⏰ Quá hạn: ${statistics.overdue}
⚠️ Sắp đến hạn (${query.warningDays} ngày): ${statistics.nearDeadline}
✅ Còn thời gian: ${statistics.onTime}

🔥 CÔNG VIỆC QUÁ HẠN (${statistics.overdueItems.length}):
${statistics.overdueItems.map(item =>
  `- ${item.title} (${item.daysRemaining} ngày) - ${item.assigneeName || 'Chưa giao'}`
).join('\n')}

⚡ SẮP ĐẾN HẠN (${statistics.nearDeadlineItems.length}):
${statistics.nearDeadlineItems.map(item =>
  `- ${item.title} (còn ${item.daysRemaining} ngày) - ${item.assigneeName || 'Chưa giao'}`
).join('\n')}

📈 THỐNG KÊ THEO DỰ ÁN:
${Object.entries(statistics.byProject).map(([project, stats]) =>
  `- ${project}: Quá hạn: ${stats.overdue}, Sắp hạn: ${stats.nearDeadline}, Đúng hạn: ${stats.onTime}`
).join('\n')}

👥 THỐNG KÊ THEO NGƯỜI:
${Object.entries(statistics.byAssignee).map(([assignee, stats]) =>
  `- ${assignee}: Quá hạn: ${stats.overdue}, Sắp hạn: ${stats.nearDeadline}, Đúng hạn: ${stats.onTime}`
).join('\n')}`;

            return summary;
          } catch (error) {
            return `Lấy thống kê deadline thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_deadline_statistics',
          description: 'Lấy thống kê deadline của công việc với nhiều bộ lọc',
          schema: z.object({
            projectId: z.number().optional().describe('Lọc theo ID dự án'),
            assigneeId: z.number().optional().describe('Lọc theo ID người được giao'),
            tags: z.array(z.string()).optional().describe('Lọc theo danh sách tags'),
            warningDays: z.number().optional().default(3).describe('Số ngày cảnh báo trước deadline (mặc định 3)'),
          }),
        }
      ),

      // Lấy công việc quá hạn với deadline
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              projectId: _args.projectId,
              assigneeId: _args.assigneeId,
              tags: _args.tags,
              warningDays: 0, // Chỉ lấy quá hạn
            };

            const statistics = await this.todoService.getDeadlineStatistics(tenantId, query);
            const overdueItems = statistics.overdueItems;

            if (overdueItems.length === 0) {
              return '🎉 Không có công việc nào quá hạn!';
            }

            const overdueList = `🚨 DANH SÁCH CÔNG VIỆC QUÁ HẠN (${overdueItems.length}):

${overdueItems.map((item, index) => {
              const deadlineDate = new Date(item.deadline!).toLocaleDateString('vi-VN');
              const daysOverdue = Math.abs(item.daysRemaining);
              return `${index + 1}. 📋 ${item.title}
   👤 Người thực hiện: ${item.assigneeName || 'Chưa giao'}
   📅 Deadline: ${deadlineDate}
   ⏰ Quá hạn: ${daysOverdue} ngày
   🏷️ Dự án: ${item.projectName || 'Không có'}
   🔖 Tags: ${item.tags?.join(', ') || 'Không có'}
   📊 Trạng thái: ${item.status}
   ⭐ Ưu tiên: ${item.priority || 'Không có'}`;
            }).join('\n\n')}

💡 Gợi ý: Hãy liên hệ với người thực hiện để cập nhật tiến độ hoặc điều chỉnh deadline.`;

            return overdueList;
          } catch (error) {
            return `Lấy danh sách công việc quá hạn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_overdue_todos_with_deadline',
          description: 'Lấy danh sách chi tiết công việc quá hạn với thông tin deadline',
          schema: z.object({
            projectId: z.number().optional().describe('Lọc theo ID dự án'),
            assigneeId: z.number().optional().describe('Lọc theo ID người được giao'),
            tags: z.array(z.string()).optional().describe('Lọc theo danh sách tags'),
          }),
        }
      ),

      // Lấy công việc sắp đến hạn
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              projectId: _args.projectId,
              assigneeId: _args.assigneeId,
              tags: _args.tags,
              warningDays: _args.warningDays || 3,
            };

            const statistics = await this.todoService.getDeadlineStatistics(tenantId, query);
            const nearDeadlineItems = statistics.nearDeadlineItems;

            if (nearDeadlineItems.length === 0) {
              return `✅ Không có công việc nào sắp đến hạn trong ${query.warningDays} ngày tới!`;
            }

            const nearDeadlineList = `⚠️ DANH SÁCH CÔNG VIỆC SẮP ĐẾN HẠN (${nearDeadlineItems.length}):

${nearDeadlineItems.map((item, index) => {
              const deadlineDate = new Date(item.deadline!).toLocaleDateString('vi-VN');
              const daysLeft = item.daysRemaining;
              const urgencyIcon = daysLeft <= 1 ? '🔥' : daysLeft <= 2 ? '⚡' : '⏰';

              return `${index + 1}. ${urgencyIcon} ${item.title}
   👤 Người thực hiện: ${item.assigneeName || 'Chưa giao'}
   📅 Deadline: ${deadlineDate}
   ⏳ Còn lại: ${daysLeft} ngày
   🏷️ Dự án: ${item.projectName || 'Không có'}
   🔖 Tags: ${item.tags?.join(', ') || 'Không có'}
   📊 Trạng thái: ${item.status}
   ⭐ Ưu tiên: ${item.priority || 'Không có'}`;
            }).join('\n\n')}

💡 Gợi ý: Hãy ưu tiên hoàn thành những công việc có deadline gần nhất.`;

            return nearDeadlineList;
          } catch (error) {
            return `Lấy danh sách công việc sắp đến hạn thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_near_deadline_todos',
          description: 'Lấy danh sách chi tiết công việc sắp đến hạn',
          schema: z.object({
            projectId: z.number().optional().describe('Lọc theo ID dự án'),
            assigneeId: z.number().optional().describe('Lọc theo ID người được giao'),
            tags: z.array(z.string()).optional().describe('Lọc theo danh sách tags'),
            warningDays: z.number().optional().default(3).describe('Số ngày cảnh báo trước deadline (mặc định 3)'),
          }),
        }
      ),

      // Xóa nhiều công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const bulkDeleteDto = {
              ids: _args.todoIds,
            };

            const result = await this.todoService.bulkDeleteTodos(tenantId, userId, bulkDeleteDto);
            return `Xóa nhiều công việc hoàn tất:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Xóa nhiều công việc thất bại: ${error.message}`;
          }
        },
        {
          name: 'bulk_delete_todos',
          description: 'Xóa nhiều công việc cùng lúc',
          schema: z.object({
            todoIds: z.array(z.number()).describe('Danh sách ID công việc cần xóa'),
          }),
        }
      ),

      // Lấy lịch sử chấm điểm của công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const scores = await this.todoService.getTodoScoreHistory(tenantId, _args.todoId);

            if (scores.length === 0) {
              return `Công việc chưa có lịch sử chấm điểm nào.`;
            }

            const scoreHistory = `📊 LỊCH SỬ CHẤM ĐIỂM:

${scores.map((score, index) => {
              const scoreDate = score.createdAt ? new Date(score.createdAt).toLocaleString('vi-VN') : 'Không xác định';
              return `${index + 1}. ⭐ ${score.awardedStars}/5 sao
   👤 Người chấm: ID ${score.scorerId}
   📅 Thời gian: ${scoreDate}
   💬 Phản hồi: ${score.feedback || 'Không có'}`;
            }).join('\n\n')}

📈 Điểm trung bình: ${(scores.reduce((sum, score) => sum + score.awardedStars, 0) / scores.length).toFixed(1)}/5 sao`;

            return scoreHistory;
          } catch (error) {
            return `Lấy lịch sử chấm điểm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_score_history',
          description: 'Lấy lịch sử chấm điểm của công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),
    ];
  }
}
