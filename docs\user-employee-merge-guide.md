# 🔄 User-Employee Merge - Hướng Dẫn Hoàn Chỉnh

## 📋 Tổng Quan

Dự án đã hoàn thành việc gộp User entity vào Employee entity để:
- Loại bỏ duplicate data giữa users và employees tables
- Sử dụng Employee entity duy nhất cho cả authentication và HR management
- Đơn giản hóa codebase và database schema

## ✅ Trạng Thái Hiện Tại

### Đã Hoàn Thành
- [x] Cập nhật tất cả entity references (user_id -> employee_id)
- [x] Thêm deprecation warnings cho UserRepository
- [x] Cập nhật Services và Controllers
- [x] Loại bỏ User entity khỏi module imports
- [x] Tạo migration script để gộp dữ liệu
- [x] Xóa User entity file
- [x] Tạo test và cleanup scripts

### Employee Entity Sẵn Sàng
Employee entity đã có đầy đủ:
- ✅ Authentication fields: `email`, `password`, `accountStatus`
- ✅ Helper methods: `getUserStatus()`, `canLogin()`, `getDisplayName()`
- ✅ Tenant isolation: `tenantId` required
- ✅ HR fields: employee info, department, manager, etc.

## 🚀 Cách Thực Hiện Migration

### Bước 1: Backup Database
```bash
# Backup toàn bộ database
pg_dump your_database > backup_before_merge.sql
```

### Bước 2: Chạy Migration
```bash
# Chạy migration script
psql -d your_database -f database/migrations/final-user-employee-merge.sql
```

### Bước 3: Verify Migration
```bash
# Chạy test script
node scripts/test-user-employee-merge.js
```

### Bước 4: Test Application
```bash
# Start application và test authentication
npm run start:dev

# Test các API endpoints
# - Login với Employee credentials
# - CRUD operations
# - Permission checks
```

### Bước 5: Cleanup (Optional)
```bash
# Chạy cleanup script
chmod +x scripts/cleanup-user-entity.sh
./scripts/cleanup-user-entity.sh
```

## 📊 Migration Script Chi Tiết

Migration script `final-user-employee-merge.sql` thực hiện:

1. **Backup & Validation**
   - Tạo `users_backup` table
   - Validate tất cả users có employee tương ứng

2. **Foreign Key Updates**
   - Cập nhật `user_roles.user_id` -> `employees.id`
   - Cập nhật `user_has_permissions.user_id` -> `employees.id`
   - Cập nhật `social_accounts.user_id` -> `employees.id`
   - Cập nhật các bảng khác nếu tồn tại

3. **Drop Users Table**
   - Xóa `users` table sau khi cập nhật xong

4. **Create Indexes**
   - Tạo indexes cho Employee authentication

## 🔍 Verification Checklist

### Database Level
- [ ] Users table đã được xóa
- [ ] Users_backup table tồn tại và có dữ liệu
- [ ] Tất cả foreign key references point đến employees.id
- [ ] Employees có authentication data (email, password, accountStatus)
- [ ] Authentication indexes đã được tạo

### Application Level
- [ ] Authentication hoạt động với Employee entity
- [ ] Tất cả APIs liên quan user management hoạt động
- [ ] Permission system hoạt động đúng
- [ ] Tenant isolation vẫn hoạt động
- [ ] Không có runtime errors

### Code Level
- [ ] Không còn active imports của User entity
- [ ] UserRepository có deprecation warnings
- [ ] Module imports đã được cập nhật
- [ ] TypeScript compilation thành công

## ⚠️ Rollback Plan

Nếu cần rollback:

1. **Restore Users Table**
```sql
-- Restore từ backup
CREATE TABLE users AS SELECT * FROM users_backup;

-- Restore foreign key references
UPDATE user_roles SET user_id = (
  SELECT u.id FROM users u 
  INNER JOIN employees e ON LOWER(u.email) = LOWER(e.email)
  WHERE e.id = user_roles.user_id
);
-- Tương tự cho các bảng khác
```

2. **Restore Code**
```bash
# Restore User entity từ git history
git checkout HEAD~1 -- src/modules/auth/entities/user.entity.ts

# Restore module imports
# Uncomment User entity imports trong các module files
```

## 🔧 Troubleshooting

### Lỗi Thường Gặp

1. **"User entity not found"**
   - Nguyên nhân: Code vẫn import User entity
   - Giải pháp: Comment out hoặc xóa import

2. **"Foreign key constraint violation"**
   - Nguyên nhân: Migration chưa cập nhật hết foreign keys
   - Giải pháp: Chạy lại migration script

3. **"Authentication failed"**
   - Nguyên nhân: Employee entity chưa có authentication data
   - Giải pháp: Verify migration đã copy password và accountStatus

### Debug Commands

```sql
-- Check employees authentication data
SELECT COUNT(*) as total, 
       COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as with_password,
       COUNT(CASE WHEN account_status = 'ACTIVE' THEN 1 END) as active
FROM employees;

-- Check foreign key references
SELECT 'user_roles' as table_name, COUNT(*) as records,
       COUNT(CASE WHEN e.id IS NOT NULL THEN 1 END) as valid_refs
FROM user_roles ur
LEFT JOIN employees e ON ur.user_id = e.id;
```

## 📚 Tài Liệu Liên Quan

- [User-Employee Merge Implementation Plan](./user-employee-merge-implementation-plan.md)
- [User-Employee Merge Completed](./user-employee-merge-completed.md)
- [Employee Entity Documentation](../src/modules/hrm/employees/entities/employee.entity.ts)

## 🎯 Kết Luận

Việc gộp User vào Employee đã hoàn thành thành công:
- ✅ Loại bỏ duplicate data
- ✅ Đơn giản hóa authentication flow
- ✅ Maintain tenant isolation
- ✅ Preserve tất cả functionality

Hệ thống giờ đây sử dụng Employee entity duy nhất cho cả authentication và HR management, giúp codebase sạch hơn và dễ maintain hơn.
