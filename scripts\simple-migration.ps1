# Simple PowerShell script for User-Employee Migration

Write-Host "🚀 User-Employee Migration Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Yellow

# Load environment variables
if (Test-Path ".env.development") {
    Get-Content ".env.development" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✅ Loaded .env.development" -ForegroundColor Green
}

# Get database config
$DB_HOST = if ($env:DB_HOST) { $env:DB_HOST } else { "localhost" }
$DB_PORT = if ($env:DB_PORT) { $env:DB_PORT } else { "5432" }
$DB_DATABASE = if ($env:DB_DATABASE) { $env:DB_DATABASE } else { "redai_db" }
$DB_USERNAME = if ($env:DB_USERNAME) { $env:DB_USERNAME } else { "postgres" }
$DB_PASSWORD = if ($env:DB_PASSWORD) { $env:DB_PASSWORD } else { "postgres" }

Write-Host ""
Write-Host "📋 Database Configuration:" -ForegroundColor Cyan
Write-Host "  Host: $DB_HOST"
Write-Host "  Port: $DB_PORT"
Write-Host "  Database: $DB_DATABASE"
Write-Host "  Username: $DB_USERNAME"
Write-Host ""

# Step 1: Test connection
Write-Host "🔗 Step 1: Testing database connection..." -ForegroundColor Yellow
if (Test-Path "scripts/test-user-employee-merge.js") {
    try {
        node scripts/test-user-employee-merge.js
        Write-Host "✅ Connection test completed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Connection test failed: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Test script not found" -ForegroundColor Yellow
}

# Step 2: Create backup
Write-Host ""
Write-Host "💾 Step 2: Creating backup..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "backup/db_backup_$timestamp.sql"

if (-not (Test-Path "backup")) {
    New-Item -ItemType Directory -Path "backup" | Out-Null
}

if (Get-Command pg_dump -ErrorAction SilentlyContinue) {
    $env:PGPASSWORD = $DB_PASSWORD
    try {
        & pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE > $backupFile
        Write-Host "✅ Backup created: $backupFile" -ForegroundColor Green
    } catch {
        Write-Host "❌ Backup failed: $_" -ForegroundColor Red
    } finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "⚠️  pg_dump not found, skipping backup" -ForegroundColor Yellow
}

# Step 3: Run migration
Write-Host ""
Write-Host "🔄 Step 3: Running migration..." -ForegroundColor Yellow
$migrationFile = "database/migrations/final-user-employee-merge.sql"

if (Test-Path $migrationFile) {
    if (Get-Command psql -ErrorAction SilentlyContinue) {
        $env:PGPASSWORD = $DB_PASSWORD
        try {
            & psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -f $migrationFile
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
            } else {
                Write-Host "❌ Migration failed!" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Migration error: $_" -ForegroundColor Red
        } finally {
            Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        }
    } else {
        Write-Host "❌ psql not found!" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Migration file not found: $migrationFile" -ForegroundColor Red
}

# Step 4: Run tests
Write-Host ""
Write-Host "🧪 Step 4: Running verification tests..." -ForegroundColor Yellow
if (Test-Path "scripts/test-user-employee-merge.js") {
    try {
        node scripts/test-user-employee-merge.js
        Write-Host "✅ Tests completed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Tests failed: $_" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  Test script not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Migration process completed!" -ForegroundColor Green
Write-Host "Check the output above for any errors." -ForegroundColor Cyan
